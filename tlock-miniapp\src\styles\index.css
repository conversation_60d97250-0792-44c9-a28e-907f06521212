@tailwind base;
@tailwind components;
@tailwind utilities;

/* 数字滚动动画 - 平滑滑动效果 */
.digit-container {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.digit-slide {
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global styles */
@layer base {
  html {
    font-family: 'HarmonyOS Sans SC', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-dark-bg text-dark-text-primary;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    -webkit-tap-highlight-color: transparent;
  }

  * {
    box-sizing: border-box;
  }

  /* Prevent zoom on iOS */
  input, select, textarea {
    font-size: 16px !important;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Hide scrollbar but keep functionality */
  ::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
}

/* Component styles */
@layer components {
  .btn-primary {
    @apply bg-gradient-primary text-white font-semibold py-3 px-6 rounded-xl touch-target transition-transform active:scale-95;
  }

  .btn-secondary {
    @apply bg-dark-surface text-dark-text-primary border border-dark-border font-semibold py-3 px-6 rounded-xl touch-target transition-transform active:scale-95;
  }

  .card {
    @apply bg-dark-surface rounded-2xl p-6 border border-dark-border;
  }

  .nav-item {
    @apply flex flex-col items-center py-2 px-4 touch-target transition-colors;
  }

  .nav-item-active {
    @apply nav-item text-primary-500;
  }

  .nav-item-inactive {
    @apply nav-item text-dark-text-muted hover:text-dark-text-secondary;
  }
}

/* Utility styles */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, theme('colors.gradient.blue') 0%, theme('colors.gradient.cyan') 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 1s ease-in-out infinite;
  }
}

/* Telegram Web App specific styles */
.telegram-viewport {
  height: var(--tg-viewport-height, 100vh);
}

.telegram-stable-viewport {
  height: var(--tg-viewport-stable-height, 100vh);
}

/* Theme integration */
:root {
  --tg-theme-bg-color: #171717;
  --tg-theme-text-color: #ffffff;
  --tg-theme-hint-color: #a1a1aa;
  --tg-theme-link-color: #259aee;
  --tg-theme-button-color: #259aee;
  --tg-theme-button-text-color: #ffffff;
  --tg-theme-secondary-bg-color: #202428;
  --tg-viewport-height: 100vh;
  --tg-viewport-stable-height: 100vh;
  --tg-color-scheme: dark;
}

/* Light theme support */
@media (prefers-color-scheme: light) {
  :root {
    --tg-theme-bg-color: #ffffff;
    --tg-theme-text-color: #0f172a;
    --tg-theme-hint-color: #64748b;
    --tg-theme-secondary-bg-color: #f8fafc;
    --tg-color-scheme: light;
  }
}

/* Custom animations */
@keyframes bounceGentle {
  0%, 100% { 
    transform: translateY(0); 
  }
  50% { 
    transform: translateY(-5px); 
  }
}

/* Loading states */
.loading-skeleton {
  @apply bg-dark-surface animate-pulse rounded;
}

.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Loading progress animation for startup page */
@keyframes progress {
  from {
    stroke-dashoffset: 283;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* Touch target for better mobile interaction */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}
