import React, { useState, useEffect } from 'react';
import { getTelegramLoginParams } from '../services/api';
import { telegramWebApp } from '../utils/telegram';

export const InviteDebugPage: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [currentUrl, setCurrentUrl] = useState<string>('');

  useEffect(() => {
    // 获取当前URL
    setCurrentUrl(window.location.href);
    
    // 获取调试信息
    const rawStartParam = telegramWebApp.getStartParam();
    const loginParams = getTelegramLoginParams();
    
    const debugData = {
      // 原始数据
      raw: {
        currentUrl: window.location.href,
        startParam: rawStartParam,
        searchParams: Object.fromEntries(new URLSearchParams(window.location.search))
      },
      // 解析后的数据
      parsed: {
        loginParams: loginParams,
        invitationCodeOnly: loginParams.invitationCode
      },
      // 解析过程
      process: {
        hasRefPrefix: rawStartParam?.startsWith('ref_') || false,
        originalStartParam: rawStartParam,
        finalInvitationCode: loginParams.invitationCode,
        isCorrectlyParsed: rawStartParam ? 
          (rawStartParam.startsWith('ref_') ? 
            rawStartParam.substring(4) === loginParams.invitationCode : 
            rawStartParam === loginParams.invitationCode) : 
          true
      }
    };
    
    setDebugInfo(debugData);
    console.log('🔍 邀请码调试信息:', debugData);
  }, []);

  const parseInvitationCode = (startParam: string | null): string => {
    if (!startParam) {
      return "";
    }
    
    if (startParam.startsWith("ref_")) {
      return startParam.substring(4);
    }
    
    return startParam;
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <h1 className="text-2xl font-bold mb-6 text-center text-blue-400">
        🔍 邀请码解析调试
      </h1>
      
      {/* 当前URL */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-green-400">当前URL</h2>
        <div className="text-xs break-all">
          {currentUrl}
        </div>
      </div>

      {debugInfo && (
        <>
          {/* 原始数据 */}
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-yellow-400">原始数据</h2>
            <div className="space-y-2 text-sm">
              <div>
                <strong className="text-blue-300">Start Param:</strong> 
                <span className="ml-2 font-mono bg-gray-700 px-2 py-1 rounded">
                  {debugInfo.raw.startParam || '(空)'}
                </span>
              </div>
              <div>
                <strong className="text-blue-300">URL Search Params:</strong>
                <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-x-auto">
                  {JSON.stringify(debugInfo.raw.searchParams, null, 2)}
                </pre>
              </div>
            </div>
          </div>

          {/* 解析过程 */}
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-purple-400">解析过程</h2>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-blue-300">包含ref_前缀:</span>
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  debugInfo.process.hasRefPrefix ? 'bg-green-600' : 'bg-red-600'
                }`}>
                  {debugInfo.process.hasRefPrefix ? '是' : '否'}
                </span>
              </div>
              
              <div>
                <span className="text-blue-300">原始参数:</span>
                <span className="ml-2 font-mono bg-gray-700 px-2 py-1 rounded">
                  {debugInfo.process.originalStartParam || '(空)'}
                </span>
              </div>
              
              <div>
                <span className="text-blue-300">解析结果:</span>
                <span className="ml-2 font-mono bg-gray-700 px-2 py-1 rounded">
                  {debugInfo.process.finalInvitationCode || '(空)'}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-blue-300">解析正确:</span>
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  debugInfo.process.isCorrectlyParsed ? 'bg-green-600' : 'bg-red-600'
                }`}>
                  {debugInfo.process.isCorrectlyParsed ? '✅ 是' : '❌ 否'}
                </span>
              </div>
            </div>
          </div>

          {/* 最终登录参数 */}
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-cyan-400">最终登录参数</h2>
            <pre className="text-xs bg-gray-900 p-3 rounded overflow-x-auto">
              {JSON.stringify(debugInfo.parsed.loginParams, null, 2)}
            </pre>
          </div>

          {/* 测试用例 */}
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-orange-400">测试用例</h2>
            <div className="space-y-2 text-sm">
              <div>
                <strong>测试1:</strong> ref_0PVHO4 → 
                <span className="ml-2 font-mono bg-gray-700 px-1">
                  {parseInvitationCode('ref_0PVHO4')}
                </span>
              </div>
              <div>
                <strong>测试2:</strong> 0PVHO4 → 
                <span className="ml-2 font-mono bg-gray-700 px-1">
                  {parseInvitationCode('0PVHO4')}
                </span>
              </div>
              <div>
                <strong>测试3:</strong> ref_ABC123 → 
                <span className="ml-2 font-mono bg-gray-700 px-1">
                  {parseInvitationCode('ref_ABC123')}
                </span>
              </div>
              <div>
                <strong>测试4:</strong> (空) → 
                <span className="ml-2 font-mono bg-gray-700 px-1">
                  "{parseInvitationCode(null)}"
                </span>
              </div>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-pink-400">使用说明</h2>
            <div className="text-sm space-y-2">
              <p>1. <strong>正确的邀请链接格式:</strong></p>
              <div className="bg-gray-900 p-2 rounded text-xs break-all">
                https://t.me/HabbyBabyBot?startapp=ref_0PVHO4
              </div>
              
              <p>2. <strong>解析后发送给后端的邀请码:</strong></p>
              <div className="bg-gray-900 p-2 rounded text-xs">
                0PVHO4 (已移除ref_前缀)
              </div>
              
              <p>3. <strong>如果解析不正确，请检查:</strong></p>
              <ul className="list-disc list-inside text-xs text-gray-300 ml-4">
                <li>邀请链接格式是否正确</li>
                <li>机器人是否正确设置了startapp参数</li>
                <li>浏览器控制台是否有相关错误信息</li>
              </ul>
            </div>
          </div>
        </>
      )}
    </div>
  );
}; 