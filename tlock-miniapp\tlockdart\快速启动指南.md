# TLock Bot 快速启动指南

## 🚀 快速开始

### 1. 准备工作
确保已安装 Dart SDK：
```bash
dart --version
```

### 2. 安装依赖
```bash
dart pub get
```

### 3. 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件，设置你的 Bot Token
vim .env
```

在 `.env` 文件中设置：
```env
TELEGRAM_BOT_TOKEN=你的机器人Token
WEBAPP_URL=https://tg.tlock.org
```

### 4. 获取 Bot Token
1. 在 Telegram 中找到 @BotFather
2. 发送 `/newbot` 创建新机器人
3. 按提示设置机器人名称和用户名
4. 复制获得的 Token 到 `.env` 文件

### 5. 启动机器人

#### 开发模式（推荐用于测试）
```bash
# Linux/macOS
./start.sh dev

# Windows
start.bat dev

# 或直接运行
dart run bin/teledart_bot.dart
```

#### 生产模式
```bash
# Linux/macOS
./start.sh prod

# Windows
start.bat prod
```

### 6. 测试机器人
1. 在 Telegram 中找到你的机器人
2. 发送 `/start` 命令
3. 应该会收到欢迎消息和小程序按钮

## 📋 可用命令

- `/start` - 启动 TLock 小程序
- `/help` - 显示帮助信息
- `/status` - 查看机器人状态

## ❗ 常见问题

### 1. Token 错误
```
错误: 401 Unauthorized
解决: 检查 .env 文件中的 TELEGRAM_BOT_TOKEN 是否正确
```

### 2. 网络连接问题
```
错误: Connection timeout
解决: 检查网络连接，确保可以访问 api.telegram.org
```

### 3. 依赖问题
```
错误: Package not found
解决: 运行 dart pub get 重新安装依赖
```

### 4. 语法错误
如果遇到语法错误，可以运行：
```bash
dart run test_syntax.dart
```

## 🔧 调试模式

如果需要查看详细日志：
```bash
# 设置环境变量
export LOG_LEVEL=debug

# 运行机器人
dart run bin/teledart_bot.dart
```

## 📞 获取帮助

如果遇到问题：
1. 查看完整部署文档：`TLock机器人部署文档.md`
2. 检查项目改进报告：`../项目对接改进报告.md`
3. 联系技术支持

---

**提示**: 首次运行建议使用开发模式进行测试，确认一切正常后再切换到生产模式。
