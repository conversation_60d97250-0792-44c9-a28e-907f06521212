#!/bin/bash

# Tlock Bot 状态检查脚本

# 自动检测脚本所在目录的父目录作为 BOT_DIR
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"
PID_FILE="$BOT_DIR/bot.pid"
LOG_FILE="$BOT_DIR/logs/bot.log"
ERROR_LOG="$BOT_DIR/logs/bot_error.log"

echo "📊 Tlock Bot 状态检查"
echo "=" * 50

# 检查PID文件
if [ ! -f "$PID_FILE" ]; then
    echo "❌ Bot 未运行 (PID文件不存在)"
    exit 1
fi

PID=$(cat "$PID_FILE")

# 检查进程是否存在
if ! ps -p $PID > /dev/null 2>&1; then
    echo "❌ Bot 进程不存在 (PID: $PID)"
    echo "⚠️ 清理僵尸PID文件..."
    rm -f "$PID_FILE"
    exit 1
fi

echo "✅ Bot 正在运行"
echo "📋 PID: $PID"

# 显示进程信息
echo ""
echo "📈 进程信息:"
ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem

# 显示日志文件信息
echo ""
echo "📄 日志文件:"
if [ -f "$LOG_FILE" ]; then
    LOG_SIZE=$(du -h "$LOG_FILE" | cut -f1)
    LOG_LINES=$(wc -l < "$LOG_FILE")
    echo "  普通日志: $LOG_FILE ($LOG_SIZE, $LOG_LINES 行)"
else
    echo "  普通日志: 不存在"
fi

if [ -f "$ERROR_LOG" ]; then
    ERROR_SIZE=$(du -h "$ERROR_LOG" | cut -f1)
    ERROR_LINES=$(wc -l < "$ERROR_LOG")
    echo "  错误日志: $ERROR_LOG ($ERROR_SIZE, $ERROR_LINES 行)"
else
    echo "  错误日志: 不存在"
fi

# 显示最近的日志
echo ""
echo "📝 最近日志 (最后10行):"
if [ -f "$LOG_FILE" ]; then
    tail -10 "$LOG_FILE"
else
    echo "  无日志文件"
fi

# 检查错误日志
if [ -f "$ERROR_LOG" ] && [ -s "$ERROR_LOG" ]; then
    echo ""
    echo "⚠️ 发现错误日志:"
    tail -5 "$ERROR_LOG"
fi
