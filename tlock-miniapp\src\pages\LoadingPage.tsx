import React, { useEffect, useState } from 'react';
import { Routes } from '../types/routes';
import { colors, typography, animations } from '../styles/design-system';

interface LoadingPageProps {
  onNavigate: (route: Routes) => void;
}

export const LoadingPage: React.FC<LoadingPageProps> = ({ onNavigate }) => {
  const [progress, setProgress] = useState(0);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    // Simulate loading progress
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          // Navigate to mining page after loading completes
          setTimeout(() => {
            onNavigate(Routes.MINING);
          }, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 50);

    // Show content after initial delay
    setTimeout(() => {
      setShowContent(true);
    }, 300);

    return () => clearInterval(interval);
  }, [onNavigate]);

  return (
    <div
      className="min-h-screen flex flex-col items-center justify-center relative overflow-hidden"
      style={{
        background: colors.background,
        color: colors.textPrimary,
      }}
    >
      {/* Background Pattern/Gradient */}
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(135deg, ${colors.background} 0%, #0a0a0a 50%, ${colors.background} 100%)`,
        }}
      />

      {/* Background Decorative Elements */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-20 left-10 w-32 h-32 rounded-full"
          style={{
            background: `radial-gradient(circle, ${colors.primary}40 0%, transparent 70%)`,
            animation: 'pulse 3s ease-in-out infinite',
          }}
        />
        <div
          className="absolute bottom-32 right-16 w-24 h-24 rounded-full"
          style={{
            background: `radial-gradient(circle, ${colors.success}30 0%, transparent 70%)`,
            animation: 'pulse 2s ease-in-out infinite 1s',
          }}
        />
      </div>

      {/* Main Content */}
      <div
        className={`flex flex-col items-center space-y-8 transition-all duration-1000 ${
          showContent ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        {/* Tlock Logo */}
        <div className="flex flex-col items-center space-y-4">
          {/* Logo Placeholder - This should be replaced with actual Tlock bird logo */}
          <div
            className="w-24 h-24 rounded-full flex items-center justify-center relative"
            style={{
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.success} 100%)`,
              animation: 'rotate 3s linear infinite',
            }}
          >
            {/* Bird Icon Placeholder */}
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
              <path
                d="M24 8C30.6 8 36 13.4 36 20C36 26.6 30.6 32 24 32C17.4 32 12 26.6 12 20C12 13.4 17.4 8 24 8Z"
                fill="white"
                opacity="0.9"
              />
              <path
                d="M18 18C19 18 20 19 20 20C20 21 19 22 18 22C17 22 16 21 16 20C16 19 17 18 18 18Z"
                fill="white"
              />
              <path
                d="M30 18C31 18 32 19 32 20C32 21 31 22 30 22C29 22 28 21 28 20C28 19 29 18 30 18Z"
                fill="white"
              />
              <path
                d="M20 26C20 28 22 30 24 30C26 30 28 28 28 26"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                fill="none"
              />
            </svg>

            {/* Rotating Ring */}
            <div
              className="absolute inset-0 rounded-full border-2 border-transparent"
              style={{
                borderTopColor: colors.textPrimary,
                borderRightColor: colors.textPrimary,
                animation: 'spin 2s linear infinite',
              }}
            />
          </div>

          {/* App Title */}
          <div className="text-center">
            <h1
              className="font-bold mb-2"
              style={{
                fontSize: '42px',
                fontFamily: typography.primary,
                fontWeight: typography.bold,
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.success} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              TLOCK
            </h1>
            <p
              className="opacity-80"
              style={{
                fontSize: typography.lg,
                fontFamily: typography.secondary,
                fontWeight: typography.medium,
              }}
            >
              Decentralized Social APP
            </p>
          </div>
        </div>

        {/* Loading Progress */}
        <div className="w-64 space-y-3">
          {/* Progress Bar */}
          <div
            className="w-full h-1 rounded-full overflow-hidden"
            style={{ background: colors.border }}
          >
            <div
              className="h-full rounded-full transition-all duration-300 ease-out"
              style={{
                width: `${progress}%`,
                background: `linear-gradient(90deg, ${colors.primary} 0%, ${colors.success} 100%)`,
              }}
            />
          </div>

          {/* Progress Text */}
          <div className="flex justify-between items-center">
            <span
              style={{
                fontSize: typography.sm,
                color: colors.textSecondary,
                fontFamily: typography.secondary,
              }}
            >
              Loading...
            </span>
            <span
              style={{
                fontSize: typography.sm,
                color: colors.textSecondary,
                fontFamily: typography.mono,
              }}
            >
              {progress}%
            </span>
          </div>
        </div>

        {/* Tagline */}
        <p
          className="text-center opacity-60 max-w-xs"
          style={{
            fontSize: typography.sm,
            fontFamily: typography.secondary,
            lineHeight: '1.5',
          }}
        >
          Safeguard Free Speech
        </p>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.1); }
        }
      `}</style>
    </div>
  );
};
