// 国家数据配置
export interface Country {
  code: string;        // 国家简称
  name: string;        // 国家全名
  flag: string;        // 国旗标识符（用于SVG图标）
}

export const COUNTRIES: Country[] = [
  // 主要国家
  { code: 'US', name: 'United States', flag: 'US' },
  { code: 'CN', name: 'China', flag: 'CN' },
  { code: 'JP', name: 'Japan', flag: 'JP' },
  { code: 'KR', name: 'South Korea', flag: 'KR' },
  { code: 'GB', name: 'United Kingdom', flag: 'GB' },
  { code: 'DE', name: 'Germany', flag: 'DE' },
  { code: 'FR', name: 'France', flag: 'FR' },
  { code: 'IT', name: 'Italy', flag: 'IT' },
  { code: 'ES', name: 'Spain', flag: 'ES' },
  { code: 'RU', name: 'Russia', flag: 'RU' },
  { code: 'CA', name: 'Canada', flag: 'CA' },
  { code: 'AU', name: 'Australia', flag: 'AU' },
  { code: 'BR', name: 'Brazil', flag: 'BR' },
  { code: 'IN', name: 'India', flag: 'IN' },
  { code: 'MX', name: 'Mexico', flag: 'MX' },
  
  // 欧洲国家
  { code: 'EE', name: 'Estonia', flag: 'EE' },
  { code: 'FI', name: 'Finland', flag: 'FI' },
  { code: 'SE', name: 'Sweden', flag: 'SE' },
  { code: 'NO', name: 'Norway', flag: 'NO' },
  { code: 'DK', name: 'Denmark', flag: 'DK' },
  { code: 'NL', name: 'Netherlands', flag: 'NL' },
  { code: 'BE', name: 'Belgium', flag: 'BE' },
  { code: 'CH', name: 'Switzerland', flag: 'CH' },
  { code: 'AT', name: 'Austria', flag: 'AT' },
  { code: 'PL', name: 'Poland', flag: 'PL' },
  { code: 'CZ', name: 'Czech Republic', flag: 'CZ' },
  { code: 'HU', name: 'Hungary', flag: 'HU' },
  { code: 'GR', name: 'Greece', flag: 'GR' },
  { code: 'PT', name: 'Portugal', flag: 'PT' },
  { code: 'IE', name: 'Ireland', flag: 'IE' },
  { code: 'LV', name: 'Latvia', flag: 'LV' },
  { code: 'LT', name: 'Lithuania', flag: 'LT' },
  { code: 'SK', name: 'Slovakia', flag: 'SK' },
  { code: 'SI', name: 'Slovenia', flag: 'SI' },
  { code: 'HR', name: 'Croatia', flag: 'HR' },
  { code: 'BG', name: 'Bulgaria', flag: 'BG' },
  { code: 'RO', name: 'Romania', flag: 'RO' },
  { code: 'UA', name: 'Ukraine', flag: 'UA' },
  { code: 'IS', name: 'Iceland', flag: 'IS' },
  { code: 'LU', name: 'Luxembourg', flag: 'LU' },
  { code: 'MT', name: 'Malta', flag: 'MT' },
  { code: 'CY', name: 'Cyprus', flag: 'CY' },
  
  // 亚洲国家
  { code: 'TH', name: 'Thailand', flag: 'TH' },
  { code: 'VN', name: 'Vietnam', flag: 'VN' },
  { code: 'SG', name: 'Singapore', flag: 'SG' },
  { code: 'MY', name: 'Malaysia', flag: 'MY' },
  { code: 'ID', name: 'Indonesia', flag: 'ID' },
  { code: 'PH', name: 'Philippines', flag: 'PH' },
  { code: 'TW', name: 'Taiwan', flag: 'TW' },
  { code: 'HK', name: 'Hong Kong', flag: 'HK' },
  { code: 'BD', name: 'Bangladesh', flag: 'BD' },
  { code: 'PK', name: 'Pakistan', flag: 'PK' },
  { code: 'LK', name: 'Sri Lanka', flag: 'LK' },
  { code: 'MM', name: 'Myanmar', flag: 'MM' },
  { code: 'KH', name: 'Cambodia', flag: 'KH' },
  { code: 'LA', name: 'Laos', flag: 'LA' },
  { code: 'BN', name: 'Brunei', flag: 'BN' },
  { code: 'MN', name: 'Mongolia', flag: 'MN' },
  { code: 'KZ', name: 'Kazakhstan', flag: 'KZ' },
  { code: 'UZ', name: 'Uzbekistan', flag: 'UZ' },
  
  // 中东和非洲
  { code: 'TR', name: 'Turkey', flag: 'TR' },
  { code: 'IL', name: 'Israel', flag: 'IL' },
  { code: 'AE', name: 'United Arab Emirates', flag: 'AE' },
  { code: 'SA', name: 'Saudi Arabia', flag: 'SA' },
  { code: 'EG', name: 'Egypt', flag: 'EG' },
  { code: 'ZA', name: 'South Africa', flag: 'ZA' },
  { code: 'NG', name: 'Nigeria', flag: 'NG' },
  { code: 'KE', name: 'Kenya', flag: 'KE' },
  { code: 'MA', name: 'Morocco', flag: 'MA' },
  { code: 'TN', name: 'Tunisia', flag: 'TN' },
  { code: 'DZ', name: 'Algeria', flag: 'DZ' },
  { code: 'ET', name: 'Ethiopia', flag: 'ET' },
  { code: 'GH', name: 'Ghana', flag: 'GH' },
  { code: 'SN', name: 'Senegal', flag: 'SN' },
  { code: 'UG', name: 'Uganda', flag: 'UG' },
  { code: 'TZ', name: 'Tanzania', flag: 'TZ' },
  { code: 'ZW', name: 'Zimbabwe', flag: 'ZW' },
  { code: 'BW', name: 'Botswana', flag: 'BW' },
  { code: 'NA', name: 'Namibia', flag: 'NA' },
  { code: 'ZM', name: 'Zambia', flag: 'ZM' },
  { code: 'MW', name: 'Malawi', flag: 'MW' },
  { code: 'MZ', name: 'Mozambique', flag: 'MZ' },
  { code: 'AO', name: 'Angola', flag: 'AO' },
  { code: 'CM', name: 'Cameroon', flag: 'CM' },
  { code: 'CI', name: 'Ivory Coast', flag: 'CI' },
  { code: 'BF', name: 'Burkina Faso', flag: 'BF' },
  { code: 'ML', name: 'Mali', flag: 'ML' },
  { code: 'NE', name: 'Niger', flag: 'NE' },
  { code: 'TD', name: 'Chad', flag: 'TD' },
  { code: 'CF', name: 'Central African Republic', flag: 'CF' },
  { code: 'CG', name: 'Republic of the Congo', flag: 'CG' },
  { code: 'GA', name: 'Gabon', flag: 'GA' },
  { code: 'GQ', name: 'Equatorial Guinea', flag: 'GQ' },
  { code: 'ST', name: 'São Tomé and Príncipe', flag: 'ST' },
  { code: 'CV', name: 'Cape Verde', flag: 'CV' },
  { code: 'GM', name: 'Gambia', flag: 'GM' },
  { code: 'GW', name: 'Guinea-Bissau', flag: 'GW' },
  { code: 'GN', name: 'Guinea', flag: 'GN' },
  { code: 'SL', name: 'Sierra Leone', flag: 'SL' },
  { code: 'LR', name: 'Liberia', flag: 'LR' },
  { code: 'BJ', name: 'Benin', flag: 'BJ' },
  { code: 'TG', name: 'Togo', flag: 'TG' },
  { code: 'RW', name: 'Rwanda', flag: 'RW' },
  { code: 'BI', name: 'Burundi', flag: 'BI' },
  { code: 'DJ', name: 'Djibouti', flag: 'DJ' },
  { code: 'SO', name: 'Somalia', flag: 'SO' },
  { code: 'ER', name: 'Eritrea', flag: 'ER' },
  { code: 'SD', name: 'Sudan', flag: 'SD' },
  { code: 'SS', name: 'South Sudan', flag: 'SS' },
  { code: 'LY', name: 'Libya', flag: 'LY' },
  { code: 'MR', name: 'Mauritania', flag: 'MR' },
  { code: 'MU', name: 'Mauritius', flag: 'MU' },
  { code: 'SC', name: 'Seychelles', flag: 'SC' },
  { code: 'MG', name: 'Madagascar', flag: 'MG' },
  { code: 'KM', name: 'Comoros', flag: 'KM' },
  
  // 美洲国家
  { code: 'AR', name: 'Argentina', flag: 'AR' },
  { code: 'CL', name: 'Chile', flag: 'CL' },
  { code: 'CO', name: 'Colombia', flag: 'CO' },
  { code: 'PE', name: 'Peru', flag: 'PE' },
  { code: 'VE', name: 'Venezuela', flag: 'VE' },
  { code: 'EC', name: 'Ecuador', flag: 'EC' },
  { code: 'BO', name: 'Bolivia', flag: 'BO' },
  { code: 'UY', name: 'Uruguay', flag: 'UY' },
  { code: 'PY', name: 'Paraguay', flag: 'PY' },
  { code: 'GY', name: 'Guyana', flag: 'GY' },
  { code: 'SR', name: 'Suriname', flag: 'SR' },
  { code: 'GT', name: 'Guatemala', flag: 'GT' },
  { code: 'BZ', name: 'Belize', flag: 'BZ' },
  { code: 'SV', name: 'El Salvador', flag: 'SV' },
  { code: 'HN', name: 'Honduras', flag: 'HN' },
  { code: 'NI', name: 'Nicaragua', flag: 'NI' },
  { code: 'CR', name: 'Costa Rica', flag: 'CR' },
  { code: 'PA', name: 'Panama', flag: 'PA' },
  { code: 'CU', name: 'Cuba', flag: 'CU' },
  { code: 'JM', name: 'Jamaica', flag: 'JM' },
  { code: 'HT', name: 'Haiti', flag: 'HT' },
  { code: 'DO', name: 'Dominican Republic', flag: 'DO' },
  { code: 'TT', name: 'Trinidad and Tobago', flag: 'TT' },
  { code: 'BB', name: 'Barbados', flag: 'BB' },
  { code: 'GD', name: 'Grenada', flag: 'GD' },
  { code: 'LC', name: 'Saint Lucia', flag: 'LC' },
  { code: 'VC', name: 'Saint Vincent and the Grenadines', flag: 'VC' },
  { code: 'AG', name: 'Antigua and Barbuda', flag: 'AG' },
  { code: 'DM', name: 'Dominica', flag: 'DM' },
  { code: 'KN', name: 'Saint Kitts and Nevis', flag: 'KN' },
  { code: 'BS', name: 'Bahamas', flag: 'BS' },
  
  // 大洋洲
  { code: 'NZ', name: 'New Zealand', flag: 'NZ' },
  { code: 'PG', name: 'Papua New Guinea', flag: 'PG' },
  { code: 'FJ', name: 'Fiji', flag: 'FJ' },
  { code: 'SB', name: 'Solomon Islands', flag: 'SB' },
  { code: 'VU', name: 'Vanuatu', flag: 'VU' },
  { code: 'NC', name: 'New Caledonia', flag: 'NC' },
  { code: 'PF', name: 'French Polynesia', flag: 'PF' },
  { code: 'WS', name: 'Samoa', flag: 'WS' },
  { code: 'TO', name: 'Tonga', flag: 'TO' },
  { code: 'KI', name: 'Kiribati', flag: 'KI' },
  { code: 'TV', name: 'Tuvalu', flag: 'TV' },
  { code: 'NR', name: 'Nauru', flag: 'NR' },
  { code: 'PW', name: 'Palau', flag: 'PW' },
  { code: 'FM', name: 'Micronesia', flag: 'FM' },
  { code: 'MH', name: 'Marshall Islands', flag: 'MH' },
];

// 根据国家代码查找国家信息
export const findCountryByCode = (code: string): Country | undefined => {
  return COUNTRIES.find(country => country.code === code);
};

// 根据国家名称查找国家信息
export const findCountryByName = (name: string): Country | undefined => {
  return COUNTRIES.find(country => country.name.toLowerCase() === name.toLowerCase());
};

// 获取默认国家（爱沙尼亚）
export const getDefaultCountry = (): Country => {
  return findCountryByCode('EE') || { code: 'EE', name: 'Estonia', flag: 'EE' };
};
