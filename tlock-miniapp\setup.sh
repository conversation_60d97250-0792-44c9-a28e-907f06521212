#!/bin/bash

# Tlock Telegram Mini App Setup Script
# This script helps initialize the project with all necessary dependencies and configurations

set -e

echo "🚀 Setting up Tlock Telegram Mini App..."
echo "========================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    echo "   Please upgrade Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Check if pnpm is installed, install if not
if ! command -v pnpm &> /dev/null; then
    echo "📦 Installing pnpm..."
    npm install -g pnpm
fi

echo "✅ pnpm version: $(pnpm -v)"

# Install dependencies
echo "📦 Installing project dependencies..."
pnpm install

# Create necessary directories
echo "📁 Creating project directories..."
mkdir -p src/{components/{ui,layout,features},pages,hooks,services,store,utils,types,styles,assets,constants}
mkdir -p public/{icons,images}
mkdir -p docs

# Create environment file
if [ ! -f .env ]; then
    echo "🔧 Creating environment configuration..."
    cat > .env << EOL
# Tlock Telegram Mini App Environment Configuration
# Copy this file to .env.local and fill in your actual values

# API Configuration
VITE_API_BASE_URL=https://api.tlock.xyz
VITE_API_TIMEOUT=10000

# Telegram Bot Configuration
VITE_TELEGRAM_BOT_TOKEN=your_bot_token_here
VITE_TELEGRAM_BOT_USERNAME=your_bot_username

# Application Configuration
VITE_APP_NAME=Tlock
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_TRACKING=false
VITE_ENABLE_DEBUG_MODE=true

# WebSocket Configuration
VITE_WS_URL=wss://ws.tlock.xyz
VITE_WS_RECONNECT_ATTEMPTS=5

# Storage Configuration
VITE_STORAGE_PREFIX=tlock_
VITE_CACHE_DURATION=3600000

# Social Features
VITE_MAX_FRIENDS=1000
VITE_MAX_DAILY_TASKS=10
VITE_MINING_INTERVAL=60000
EOL
    echo "✅ Created .env file with default configuration"
    echo "   Please update the values in .env with your actual configuration"
else
    echo "✅ Environment file already exists"
fi

# Create basic TypeScript configuration for tests
if [ ! -f tsconfig.node.json ]; then
    echo "🔧 Creating TypeScript configuration for Node.js..."
    cat > tsconfig.node.json << EOL
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true
  },
  "include": ["vite.config.ts", "vitest.config.ts"]
}
EOL
fi

# Create Vitest configuration
if [ ! -f vitest.config.ts ]; then
    echo "🧪 Creating Vitest configuration..."
    cat > vitest.config.ts << EOL
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        'dist/',
      ],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
EOL
fi

# Create test setup file
mkdir -p src/test
if [ ! -f src/test/setup.ts ]; then
    echo "🧪 Creating test setup..."
    cat > src/test/setup.ts << EOL
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock Telegram Web App
const mockTelegramWebApp = {
  ready: vi.fn(),
  expand: vi.fn(),
  close: vi.fn(),
  MainButton: {
    text: 'CONTINUE',
    color: '#259AEE',
    textColor: '#FFFFFF',
    isVisible: false,
    isActive: true,
    setText: vi.fn(),
    onClick: vi.fn(),
    offClick: vi.fn(),
    show: vi.fn(),
    hide: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
  },
  BackButton: {
    isVisible: false,
    onClick: vi.fn(),
    offClick: vi.fn(),
    show: vi.fn(),
    hide: vi.fn(),
  },
  initData: '',
  initDataUnsafe: {
    user: {
      id: 123456789,
      first_name: 'Test',
      last_name: 'User',
      username: 'testuser',
      language_code: 'en',
    },
  },
  colorScheme: 'dark',
  themeParams: {
    bg_color: '#171717',
    text_color: '#ffffff',
    hint_color: '#a1a1aa',
    link_color: '#259aee',
    button_color: '#259aee',
    button_text_color: '#ffffff',
    secondary_bg_color: '#202428',
  },
  isExpanded: true,
  viewportHeight: 812,
  viewportStableHeight: 812,
  headerColor: '#259AEE',
  backgroundColor: '#171717',
  onEvent: vi.fn(),
  offEvent: vi.fn(),
  sendData: vi.fn(),
  openLink: vi.fn(),
  openTelegramLink: vi.fn(),
  showPopup: vi.fn(),
  showAlert: vi.fn(),
  showConfirm: vi.fn(),
  enableClosingConfirmation: vi.fn(),
  disableClosingConfirmation: vi.fn(),
};

// Mock window.Telegram
Object.defineProperty(window, 'Telegram', {
  value: {
    WebApp: mockTelegramWebApp,
  },
  writable: true,
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});
EOL
fi

# Create basic folder structure with index files
echo "📁 Creating basic component structure..."

# Create basic UI components
mkdir -p src/components/ui
cat > src/components/ui/index.ts << EOL
// Export all UI components from this file
export { Button } from './Button';
export { Input } from './Input';
export { Card } from './Card';
export { Avatar } from './Avatar';
export { Badge } from './Badge';
export { Modal } from './Modal';
export { Toast } from './Toast';
export { Spinner } from './Spinner';
EOL

# Create constants file
cat > src/constants/index.ts << EOL
// Application constants
export const APP_CONFIG = {
  NAME: 'Tlock',
  VERSION: '1.0.0',
  DESCRIPTION: 'Decentralized Social App',
  AUTHOR: 'Tlock Team',
} as const;

export const API_ENDPOINTS = {
  AUTH: '/auth',
  USER: '/user',
  MINING: '/mining',
  REWARDS: '/rewards',
  FRIENDS: '/friends',
  TASKS: '/tasks',
} as const;

export const STORAGE_KEYS = {
  USER_DATA: 'user_data',
  AUTH_TOKEN: 'auth_token',
  THEME: 'theme',
  LANGUAGE: 'language',
  MINING_STATE: 'mining_state',
} as const;

export const ROUTES = {
  HOME: '/',
  PROFILE: '/profile',
  FRIENDS: '/friends',
  TASKS: '/tasks',
  REWARDS: '/rewards',
  SETTINGS: '/settings',
} as const;

export const THEME_COLORS = {
  PRIMARY: '#259AEE',
  SECONDARY: '#7AFFFF',
  DARK_BG: '#171717',
  DARK_SURFACE: '#202428',
  LIGHT_BG: '#FFFFFF',
  LIGHT_SURFACE: '#F8FAFC',
} as const;
EOL

# Create types file
cat > src/types/index.ts << EOL
// Global type definitions

export interface User {
  id: number;
  firstName: string;
  lastName?: string;
  username?: string;
  languageCode?: string;
  isPremium?: boolean;
  photoUrl?: string;
  level: number;
  experience: number;
  coins: number;
  miningRate: number;
  lastMiningTime: string;
}

export interface MiningState {
  isActive: boolean;
  startTime: string;
  rate: number;
  totalMined: number;
  nextClaimTime: string;
}

export interface DailyTask {
  id: string;
  title: string;
  description: string;
  reward: number;
  completed: boolean;
  type: 'social' | 'mining' | 'referral' | 'daily';
  progress: number;
  maxProgress: number;
}

export interface Friend {
  id: number;
  firstName: string;
  lastName?: string;
  username?: string;
  photoUrl?: string;
  level: number;
  isOnline: boolean;
  lastSeen: string;
  referralReward: number;
}

export interface Reward {
  id: string;
  type: 'daily' | 'task' | 'referral' | 'mining';
  amount: number;
  claimed: boolean;
  claimTime?: string;
  expiresAt?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface TelegramWebAppUser {
  id: number;
  is_bot?: boolean;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  added_to_attachment_menu?: boolean;
  allows_write_to_pm?: boolean;
  photo_url?: string;
}

export interface TelegramWebAppInitData {
  query_id?: string;
  user?: TelegramWebAppUser;
  receiver?: TelegramWebAppUser;
  chat_type?: string;
  chat_instance?: string;
  start_param?: string;
  can_send_after?: number;
  auth_date: number;
  hash: string;
}
EOL

# Run initial linting and formatting
echo "🔍 Running initial code quality checks..."
pnpm lint --fix || echo "⚠️  Some linting issues found, please review"
pnpm format || echo "⚠️  Some formatting issues found, please review"

# Run type checking
echo "🔍 Running TypeScript type checking..."
pnpm type-check || echo "⚠️  Some type issues found, please review"

echo ""
echo "🎉 Setup completed successfully!"
echo "========================================"
echo ""
echo "Next steps:"
echo "1. Update .env file with your actual configuration"
echo "2. Start development server: pnpm dev"
echo "3. Open http://localhost:3000 in your browser"
echo "4. For Telegram testing, use HTTPS and configure your bot"
echo ""
echo "Available commands:"
echo "  pnpm dev          - Start development server"
echo "  pnpm build        - Build for production"
echo "  pnpm test         - Run tests"
echo "  pnpm lint         - Check code quality"
echo "  pnpm format       - Format code"
echo ""
echo "📚 Read DEVELOPMENT_RULES.md for coding guidelines"
echo "📖 Check README.md for detailed documentation"
echo ""
echo "Happy coding! 🚀"
