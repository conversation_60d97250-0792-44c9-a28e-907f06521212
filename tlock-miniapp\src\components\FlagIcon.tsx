import React from 'react';

interface FlagIconProps {
  countryCode: string;
  size?: number;
  className?: string;
  circular?: boolean; // 是否使用圆形版本
}

export const FlagIcon: React.FC<FlagIconProps> = ({ 
  countryCode, 
  size = 22, 
  className = '',
  circular = false
}) => {
  // 确保国家代码是2位小写字母格式 (ISO 3166-1-alpha-2)
  const formatCountryCode = (code: string): string => {
    return code.toLowerCase().slice(0, 2);
  };

  const formattedCode = formatCountryCode(countryCode);

  if (circular) {
    // 使用 HatScripts/circle-flags 的完美圆形 SVG
    return (
      <img
        src={`https://hatscripts.github.io/circle-flags/flags/${formattedCode}.svg`}
        alt={`Flag of ${countryCode.toUpperCase()}`}
        className={className}
        style={{
          width: `${size}px`,
          height: `${size}px`,
          display: 'inline-block',
          borderRadius: '50%', // 确保是完美圆形
          objectFit: 'cover'
        }}
        onError={(e) => {
          // 如果圆形图标加载失败，回退到矩形flag-icons
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          // 创建备用的flag-icons span元素
          const fallbackSpan = document.createElement('span');
          fallbackSpan.className = `fi fi-${formattedCode} ${className}`;
          fallbackSpan.style.width = `${size}px`;
          fallbackSpan.style.height = `${size}px`;
          fallbackSpan.style.display = 'inline-block';
          fallbackSpan.style.borderRadius = '50%';
          fallbackSpan.style.overflow = 'hidden';
          fallbackSpan.title = `Flag of ${countryCode.toUpperCase()}`;
          target.parentNode?.insertBefore(fallbackSpan, target);
        }}
      />
    );
  } else {
    // 使用 flag-icons 的矩形版本
    const flagClasses = [
      'fi', // 基础类
      `fi-${formattedCode}`, // 国家代码类
      className
    ].filter(Boolean).join(' ');

    return (
      <span 
        className={flagClasses}
        style={{
          width: `${size}px`,
          height: `${size}px`,
          display: 'inline-block',
          borderRadius: '2px', // 轻微圆角
          overflow: 'hidden'
        }}
        title={`Flag of ${countryCode.toUpperCase()}`}
      />
    );
  }
};
