import React, { useState, useEffect } from 'react';
import { screenshotHelper } from '../utils/screenshot-helper';

export const MediaGeneratorPage: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateIcon = async () => {
    setIsGenerating(true);
    try {
      screenshotHelper.generateAppIcon();
      alert('✅ 应用图标已生成并下载！\n文件名：tlock-app-icon-512x512.png');
    } catch (error) {
      alert('❌ 图标生成失败，请检查浏览器控制台');
      console.error(error);
    }
    setIsGenerating(false);
  };

  const handleCaptureScreenshot = async (pageName: string) => {
    setIsGenerating(true);
    try {
      await screenshotHelper.captureCurrentPage(`tlock-${pageName}-screenshot`);
      alert(`✅ ${pageName}页面截图已生成！`);
    } catch (error) {
      alert('❌ 截图失败，请查看手动截图指南');
      console.error(error);
    }
    setIsGenerating(false);
  };

  const showManualInstructions = () => {
    const instructions = `
📱 Telegram Mini App 媒体准备指南

🎯 需要准备的文件：

1. 📱 App Icon (512x512 PNG)
   - 点击下面的"生成应用图标"按钮
   - 或使用设计软件制作Tlock品牌图标

2. 📸 预览截图 (1080x1920 PNG)
   建议截图页面：
   • 主页 (/mining) - 展示挖矿功能
   • 任务页 (/earn) - 展示每日任务
   • 好友页 (/friends) - 展示邀请功能
   • 聊天功能 - 点击发送按钮展示

3. 🎬 预览视频 (30秒 MP4)
   - 使用屏幕录制工具
   - 展示核心功能流程
   - 分辨率：1080x1920或375x812

📋 上传到@BotFather步骤：
1. 发送 /mybots 给 @BotFather
2. 选择 HappyBaby
3. Bot Settings → Configure Mini App
4. Enable Mini App
5. 上传媒体文件
6. 设置Mini App URL

🔗 Bot信息：
• Bot名称：HappyBaby
• Bot Token：8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk
    `;
    
    alert(instructions);
  };

  const navigateToPage = (path: string) => {
    window.location.href = path;
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-center">
          📱 Telegram媒体生成器
        </h1>

        {/* 说明 */}
        <div className="mb-6 p-4 bg-blue-900 border border-blue-600 rounded-lg">
          <h2 className="text-lg font-semibold mb-2 text-blue-400">
            ℹ️ 使用说明
          </h2>
          <p className="text-sm text-blue-200">
            此工具帮助您为HappyBaby Bot生成Telegram Mini App所需的媒体文件。
            包括应用图标、预览截图等。
          </p>
        </div>

        {/* 应用图标生成 */}
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">🎨 应用图标生成</h2>
          <p className="text-sm text-gray-300 mb-3">
            生成512x512像素的PNG应用图标，用于@BotFather配置。
          </p>
          <button
            onClick={handleGenerateIcon}
            disabled={isGenerating}
            className="w-full p-3 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {isGenerating ? '生成中...' : '🎨 生成应用图标'}
          </button>
        </div>

        {/* 截图工具 */}
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">📸 页面截图工具</h2>
          <p className="text-sm text-gray-300 mb-3">
            为不同页面生成预览截图。建议先导航到对应页面，再点击截图按钮。
          </p>
          
          <div className="space-y-2">
            <div className="flex space-x-2">
              <button
                onClick={() => navigateToPage('/mining')}
                className="flex-1 p-2 bg-green-600 rounded hover:bg-green-700 text-sm"
              >
                📍 主页
              </button>
              <button
                onClick={() => handleCaptureScreenshot('mining')}
                disabled={isGenerating}
                className="flex-1 p-2 bg-blue-600 rounded hover:bg-blue-700 text-sm disabled:opacity-50"
              >
                📸 截图
              </button>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => navigateToPage('/earn')}
                className="flex-1 p-2 bg-green-600 rounded hover:bg-green-700 text-sm"
              >
                📍 任务页
              </button>
              <button
                onClick={() => handleCaptureScreenshot('earn')}
                disabled={isGenerating}
                className="flex-1 p-2 bg-blue-600 rounded hover:bg-blue-700 text-sm disabled:opacity-50"
              >
                📸 截图
              </button>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => navigateToPage('/friends')}
                className="flex-1 p-2 bg-green-600 rounded hover:bg-green-700 text-sm"
              >
                📍 好友页
              </button>
              <button
                onClick={() => handleCaptureScreenshot('friends')}
                disabled={isGenerating}
                className="flex-1 p-2 bg-blue-600 rounded hover:bg-blue-700 text-sm disabled:opacity-50"
              >
                📸 截图
              </button>
            </div>
          </div>
        </div>

        {/* 视频录制指南 */}
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">🎬 预览视频指南</h2>
          <p className="text-sm text-gray-300 mb-3">
            录制30秒预览视频，展示应用核心功能。
          </p>
          <button
            onClick={() => screenshotHelper.createVideoFrames()}
            className="w-full p-3 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors"
          >
            📋 查看录制指南
          </button>
        </div>

        {/* 完整指南 */}
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">📋 完整配置指南</h2>
          <p className="text-sm text-gray-300 mb-3">
            查看完整的Telegram Bot配置和媒体上传指南。
          </p>
          <button
            onClick={showManualInstructions}
            className="w-full p-3 bg-orange-600 rounded-lg hover:bg-orange-700 transition-colors"
          >
            📖 查看完整指南
          </button>
        </div>

        {/* Bot信息 */}
        <div className="mb-6 p-4 bg-green-900 border border-green-600 rounded-lg">
          <h2 className="text-lg font-semibold mb-2 text-green-400">
            🤖 您的Bot信息
          </h2>
          <div className="text-sm space-y-1">
            <div><strong>Bot名称:</strong> HappyBaby</div>
            <div><strong>Token:</strong> 8054527269:AAE...</div>
            <div><strong>配置地址:</strong> @BotFather</div>
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="space-y-2">
          <button
            onClick={() => window.location.href = '/settings'}
            className="w-full p-3 bg-gray-600 rounded-lg hover:bg-gray-700 transition-colors"
          >
            ← 返回设置
          </button>
          <button
            onClick={() => window.location.href = '/mining'}
            className="w-full p-3 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
          >
            🏠 返回主页
          </button>
        </div>
      </div>
    </div>
  );
};
