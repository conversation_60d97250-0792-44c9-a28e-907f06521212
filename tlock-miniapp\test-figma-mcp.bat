@echo off
echo ========================================
echo 测试 Figma MCP 服务器连接
echo ========================================

echo.
echo 1. 检查 Node.js 版本...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

echo.
echo 2. 检查端口 3333 占用情况...
netstat -ano | findstr :3333
if %errorlevel% equ 0 (
    echo 警告: 端口 3333 已被占用，可能需要先关闭其他服务
)

echo.
echo 3. 测试 Figma API Key...
echo 正在验证 API Key: *********************************************

echo.
echo 4. 启动 Figma MCP 服务器...
echo 使用命令: npx -y figma-developer-mcp --figma-api-key=********************************************* --stdio
echo.
echo 注意: 如果看到 "Server running" 消息，说明服务器启动成功
echo 按 Ctrl+C 可以停止服务器

npx -y figma-developer-mcp --figma-api-key=********************************************* --stdio

echo.
echo 测试完成
pause
