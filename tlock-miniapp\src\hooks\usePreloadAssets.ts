import { useEffect } from 'react';

// 静态导入图片路径
import dailyRewardIcon from '../assets/icons/daily-reward-icon.png';
import readDocumentIcon from '../assets/icons/read-document-icon.png';
import shareFriendsIcon from '../assets/icons/share-friends-icon.png';
import telegramIcon from '../assets/icons/telegram-icon.png';
import twitterIcon from '../assets/icons/twitter-icon.png';
import tokenIcon from '../assets/icons/token-icon.png';

const ICONS = [
  dailyRewardIcon,
  readDocumentIcon,
  shareFriendsIcon,
  telegramIcon,
  twitterIcon,
  tokenIcon,
];

export const usePreloadAssets = () => {
  useEffect(() => {
    ICONS.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  }, []);
}; 