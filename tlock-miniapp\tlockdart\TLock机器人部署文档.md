# TLock Telegram 机器人部署文档

## 📋 概述

TLock Telegram 机器人是一个基于 Dart 语言开发的 Telegram Bot，用于为用户提供 TLock 小程序的入口和相关服务。

## 🛠️ 系统要求

### 最低配置
- **操作系统**: Linux (Ubuntu 18.04+) / Windows 10+ / macOS 10.14+
- **内存**: 512MB RAM
- **存储**: 1GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Ubuntu 20.04 LTS
- **内存**: 1GB RAM
- **存储**: 2GB 可用空间
- **CPU**: 1 核心

## 📦 环境准备

### 1. 安装 Dart SDK

#### Ubuntu/Debian
```bash
# 添加 Dart 官方源
sudo apt-get update
sudo apt-get install apt-transport-https
wget -qO- https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo gpg --dearmor -o /usr/share/keyrings/dart.gpg
echo 'deb [signed-by=/usr/share/keyrings/dart.gpg arch=amd64] https://storage.googleapis.com/download.dartlang.org/linux/debian stable main' | sudo tee /etc/apt/sources.list.d/dart_stable.list

# 安装 Dart
sudo apt-get update
sudo apt-get install dart
```

#### Windows
1. 下载 Dart SDK: https://dart.dev/get-dart
2. 解压到 `C:\dart-sdk`
3. 添加 `C:\dart-sdk\bin` 到系统 PATH

#### macOS
```bash
# 使用 Homebrew
brew tap dart-lang/dart
brew install dart
```

### 2. 验证安装
```bash
dart --version
```

## 🚀 部署步骤

### 1. 获取项目代码
```bash
# 克隆项目（如果使用 Git）
git clone <your-repository-url>
cd tlockdart

# 或者直接上传项目文件到服务器
```

### 2. 安装依赖
```bash
dart pub get
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

**必填配置项：**
```env
# 从 @BotFather 获取的 Bot Token
TELEGRAM_BOT_TOKEN=1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ

# TLock 小程序地址
WEBAPP_URL=https://tg.tlock.org
```

### 4. 创建 Telegram Bot

1. 在 Telegram 中找到 @BotFather
2. 发送 `/newbot` 创建新机器人
3. 按提示设置机器人名称和用户名
4. 获取 Bot Token 并填入 `.env` 文件
5. 设置机器人命令菜单：
   ```
   /setcommands
   选择你的机器人
   发送以下内容：
   start - 启动 TLock 小程序
   help - 显示帮助信息
   status - 查看机器人状态
   ```

### 5. 测试运行
```bash
# 直接运行测试
dart run bin/teledart_bot.dart
```

如果看到以下输出说明启动成功：
```
Bot 启动成功: @your_bot_name
TeleDart 服务已启动
所有命令处理器已注册完成
```

## 🔧 生产环境部署

### 方式一：使用 systemd (推荐)

1. **编译为可执行文件**
```bash
dart compile exe bin/teledart_bot.dart -o tlock_bot
```

2. **创建服务文件**
```bash
sudo nano /etc/systemd/system/tlock-bot.service
```

内容如下：
```ini
[Unit]
Description=TLock Telegram Bot
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/path/to/tlockdart
ExecStart=/path/to/tlockdart/tlock_bot
Restart=always
RestartSec=10
Environment=TELEGRAM_BOT_TOKEN=your_token_here
Environment=WEBAPP_URL=https://tg.tlock.org

[Install]
WantedBy=multi-user.target
```

3. **启动服务**
```bash
sudo systemctl daemon-reload
sudo systemctl enable tlock-bot
sudo systemctl start tlock-bot
```

4. **查看状态**
```bash
sudo systemctl status tlock-bot
```

### 方式二：使用 Docker

1. **创建 Dockerfile**
```dockerfile
FROM dart:stable AS build

WORKDIR /app
COPY pubspec.* ./
RUN dart pub get

COPY . .
RUN dart compile exe bin/teledart_bot.dart -o tlock_bot

FROM debian:bullseye-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY --from=build /app/tlock_bot .

CMD ["./tlock_bot"]
```

2. **构建和运行**
```bash
# 构建镜像
docker build -t tlock-bot .

# 运行容器
docker run -d \
  --name tlock-bot \
  --restart unless-stopped \
  -e TELEGRAM_BOT_TOKEN=your_token_here \
  -e WEBAPP_URL=https://tg.tlock.org \
  tlock-bot
```

## 📊 监控和维护

### 查看日志
```bash
# systemd 服务日志
sudo journalctl -u tlock-bot -f

# Docker 容器日志
docker logs -f tlock-bot
```

### 重启服务
```bash
# systemd
sudo systemctl restart tlock-bot

# Docker
docker restart tlock-bot
```

### 更新部署
```bash
# 1. 停止服务
sudo systemctl stop tlock-bot

# 2. 更新代码
git pull

# 3. 重新编译
dart compile exe bin/teledart_bot.dart -o tlock_bot

# 4. 启动服务
sudo systemctl start tlock-bot
```

## 🔒 安全建议

1. **保护 Bot Token**
   - 不要在代码中硬编码 Token
   - 使用环境变量或配置文件
   - 定期轮换 Token

2. **服务器安全**
   - 使用防火墙限制访问
   - 定期更新系统和依赖
   - 使用非 root 用户运行服务

3. **监控告警**
   - 设置服务状态监控
   - 配置日志告警
   - 监控资源使用情况

## ❗ 故障排除

### 常见问题

1. **Bot Token 无效**
   ```
   错误: 401 Unauthorized
   解决: 检查 Token 是否正确，确保从 @BotFather 获取
   ```

2. **网络连接问题**
   ```
   错误: Connection timeout
   解决: 检查网络连接，确保可以访问 api.telegram.org
   ```

3. **权限问题**
   ```
   错误: Permission denied
   解决: 检查文件权限，确保运行用户有执行权限
   ```

4. **依赖问题**
   ```
   错误: Package not found
   解决: 运行 dart pub get 重新安装依赖
   ```

### 调试模式
```bash
# 设置详细日志
export LOG_LEVEL=debug
dart run bin/teledart_bot.dart
```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查网络连接和配置
3. 参考官方文档: https://pub.dev/packages/teledart
4. 联系技术支持团队

---

**版本**: 1.0.0  
**更新日期**: 2025-01-17  
**维护者**: TLock 开发团队
