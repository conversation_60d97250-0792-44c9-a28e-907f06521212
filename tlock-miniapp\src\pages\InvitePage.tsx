import React, { useState, useEffect } from 'react';
import { InviteService, InviteStats, InvitedFriend } from '../services/inviteService';
import { useUser } from '../contexts/UserContext';

interface InvitePageProps {
  className?: string;
}

const InvitePage: React.FC<InvitePageProps> = ({ className = '' }) => {
  const { user } = useUser();
  const [inviteStats, setInviteStats] = useState<InviteStats | null>(null);
  const [invitedFriends, setInvitedFriends] = useState<InvitedFriend[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    if (user?.id) {
      loadInviteData();
    }
  }, [user]);

  const loadInviteData = async () => {
    if (!user?.id) return;
    
    setIsLoading(true);
    try {
      const [stats, friends] = await Promise.all([
        InviteService.getInviteStats(user.id),
        InviteService.getInvitedFriends(user.id)
      ]);
      
      setInviteStats(stats);
      setInvitedFriends(friends);
    } catch (error) {
      console.error('加载邀请数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteFriend = async () => {
    await InviteService.shareInviteLink('Join TLock and unlock exclusive features! 🔐✨');

    // 触发触觉反馈
    if (window.Telegram?.WebApp?.HapticFeedback) {
      window.Telegram.WebApp.HapticFeedback.impactOccurred('medium');
    }
  };

  const handleCopyLink = async () => {
    const inviteLink = await InviteService.generateInviteLink();
    if (!inviteLink) {
      console.error('无法生成邀请链接，邀请码未设置');
      return;
    }

    const success = await InviteService.copyToClipboard(inviteLink);

    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);

      // 触发触觉反馈
      if (window.Telegram?.WebApp?.HapticFeedback) {
        window.Telegram.WebApp.HapticFeedback.notificationOccurred('success');
      }
    }
  };

  // 检查是否有邀请码
  const hasInvitationCode = InviteService.getInvitationCode() !== null;

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-black text-white flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading invite data...</p>
        </div>
      </div>
    );
  }

  if (!hasInvitationCode) {
    return (
      <div className={`min-h-screen bg-black text-white flex items-center justify-center ${className}`}>
        <div className="text-center p-6">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold mb-2">邀请码未设置</h2>
          <p className="text-gray-400 mb-4">请先完成登录以获取您的邀请码</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            刷新页面
          </button>
        </div>
      </div>
    );
  }

  const rewards = InviteService.calculateInviteRewards(inviteStats?.invite_count || 0);

  return (
    <div className={`min-h-screen bg-black text-white ${className}`}>
      <div className="p-4 space-y-6">
        {/* Header */}
        <div className="text-center py-4">
          <h1 className="text-2xl font-bold text-white mb-2">Invite Friends</h1>
          <p className="text-gray-400">Earn rewards by inviting friends to TLock</p>
        </div>

        {/* User Section */}
        <div className="flex items-center space-x-4 p-4 bg-gray-900 rounded-xl">
          <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center">
            <span className="text-xl font-bold text-white">
              {user?.username ? user.username[0].toUpperCase() : 'U'}
            </span>
          </div>
          <div className="flex-1">
            <h2 className="text-lg font-bold text-white">
              {user?.username ? `@${user.username}` : user?.first_name || 'User'}
            </h2>
            <p className="text-gray-400 text-sm">
              {inviteStats?.invite_count || 0} friends invited
            </p>
          </div>
        </div>

        {/* Invite Button */}
        <button
          onClick={handleInviteFriend}
          className="w-full bg-green-500 hover:bg-green-600 text-black font-bold py-4 px-6 rounded-xl transition-colors duration-200"
        >
          Invite Friend
        </button>

        {/* Copy Link Button */}
        <button
          onClick={handleCopyLink}
          className="w-full bg-gray-800 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <span>{copySuccess ? 'Copied!' : 'Copy Invite Link'}</span>
        </button>

        {/* Reward System */}
        <div className="bg-gray-900 rounded-xl p-4">
          <div className="flex items-center space-x-2 mb-4">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-bold text-white">3 LVL's Referral System</h3>
          </div>
          
          <div className="flex items-center space-x-2 mb-4">
            <LevelBadge level="1" rate="10%" isActive={rewards.level === '1'} />
            <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <LevelBadge level="2" rate="5%" isActive={rewards.level === '2'} />
            <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <LevelBadge level="3" rate="2.5%" isActive={rewards.level === '3'} />
          </div>
          
          <p className="text-gray-400 text-sm">
            {InviteService.getRewardDescription(rewards.level)}
          </p>
        </div>

        {/* Referral Balance */}
        <div className="bg-gray-900 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">Referral Balance</p>
              <p className="text-2xl font-bold text-white">
                {InviteService.formatBalance(inviteStats?.referral_balance || 0)}
              </p>
            </div>
            <button
              disabled={!inviteStats?.referral_balance || inviteStats.referral_balance <= 0}
              className="bg-green-500 hover:bg-green-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-medium py-2 px-4 rounded-lg transition-colors duration-200"
            >
              Withdraw
            </button>
          </div>
        </div>

        {/* Friends List */}
        <div className="space-y-4">
          <h3 className="text-lg font-bold text-white">Friends</h3>
          
          {invitedFriends.length === 0 ? (
            <div className="bg-gray-900 rounded-xl p-6 text-center">
              <p className="text-gray-400">No friends invited yet</p>
              <p className="text-gray-500 text-sm mt-1">Start inviting friends to earn rewards!</p>
            </div>
          ) : (
            <div className="space-y-2">
              {invitedFriends.map((friend) => (
                <FriendItem key={friend.id} friend={friend} />
              ))}
            </div>
          )}
        </div>

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <DebugInfo />
        )}
      </div>
    </div>
  );
};

// Debug Info Component
const DebugInfo: React.FC = () => {
  const [inviteLink, setInviteLink] = useState<string | null>(null);

  useEffect(() => {
    const loadInviteLink = async () => {
      const link = await InviteService.generateInviteLink();
      setInviteLink(link);
    };
    loadInviteLink();
  }, []);

  return (
    <div className="bg-gray-800 rounded-lg p-3 text-xs">
      <p className="text-gray-400">Debug Info:</p>
      <p className="text-white">Invitation Code: {InviteService.getInvitationCode()}</p>
      <p className="text-white">Invite Link: {inviteLink || 'Loading...'}</p>
    </div>
  );
};

// Level Badge Component
interface LevelBadgeProps {
  level: string;
  rate: string;
  isActive: boolean;
}

const LevelBadge: React.FC<LevelBadgeProps> = ({ level, rate, isActive }) => (
  <div className={`px-3 py-1 rounded-md text-xs font-bold ${
    isActive
      ? 'bg-green-500 text-black'
      : 'bg-gray-800 text-gray-400'
  }`}>
    {level} LVL - {rate}
  </div>
);

// Friend Item Component
interface FriendItemProps {
  friend: InvitedFriend;
}

const FriendItem: React.FC<FriendItemProps> = ({ friend }) => (
  <div className="bg-gray-900 rounded-lg p-3 flex items-center space-x-3">
    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
      <span className="text-sm font-bold text-white">
        {friend.username ? friend.username[0].toUpperCase() : friend.first_name[0].toUpperCase()}
      </span>
    </div>
    <div className="flex-1">
      <p className="text-white font-medium">
        {friend.username ? `@${friend.username}` : friend.first_name}
      </p>
      <p className="text-gray-400 text-xs">
        Joined {new Date(friend.joined_date).toLocaleDateString()}
      </p>
    </div>
    <div className={`w-2 h-2 rounded-full ${
      friend.status === 'active' ? 'bg-green-500' : 'bg-gray-500'
    }`} />
  </div>
);

export default InvitePage;
