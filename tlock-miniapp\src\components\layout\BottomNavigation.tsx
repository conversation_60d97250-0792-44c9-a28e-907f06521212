import React from 'react';
import { Routes } from '../../types/routes';
import { ENV_CONFIG, shouldUseEmbeddedViewer } from '../../config/environment';
import { telegramWebApp } from '../../utils/telegram';

// Import SVG icons
import MiningIcon from '../../assets/icons/navigation/mining-icon.svg';
import DocIcon from '../../assets/icons/navigation/doc-icon.svg';
import EarnIcon from '../../assets/icons/navigation/earn-icon.svg';
import FriendsIcon from '../../assets/icons/navigation/friends-icon.svg';

interface BottomNavigationProps {
  currentRoute: Routes;
  onNavigate: (route: Routes) => void;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  currentRoute,
  onNavigate,
}) => {
  const navItems = [
    {
      route: Routes.MINING,
      label: 'Mining',
      icon: MiningIcon,
    },
    {
      route: Routes.DOCUMENTS,
      label: 'Doc',
      icon: DocIcon,
    },
    {
      route: Routes.EARN,
      label: 'Earn',
      icon: EarnIcon,
    },
    {
      route: Routes.FRIENDS,
      label: 'Friends',
      icon: FriendsIcon,
    },
  ];

  // 调试：确认所有菜单项
  React.useEffect(() => {
    console.log('BottomNavigation 渲染菜单项:', navItems.map(item => item.label));
    console.log('当前路由:', currentRoute);
    console.log('菜单项数量:', navItems.length);
  }, [currentRoute]);

  return (
    <nav
      className="absolute bottom-0 left-0 right-0 h-[68px]"
      style={{
        backgroundColor: '#101010',
        borderTop: '0.494px solid #1A1A1A',
        minWidth: '375px', // 确保容器宽度足够
        maxWidth: '100vw'   // 允许使用全屏宽度
      }}
    >
      <div className="flex justify-around items-center h-full px-4">
        {navItems.map((item, index) => {
          const isActive = currentRoute === item.route;

          const handleClick = () => {
            onNavigate(item.route);
          };

          return (
            <button
              key={item.route}
              onClick={handleClick}
              className="flex flex-col items-center justify-center touch-target transition-colors flex-1"
              style={{
                height: '54px'
              }}
            >
              {/* 图标区域 */}
              <div className="w-7 h-7 flex items-center justify-center mb-1">
                <img
                  src={item.icon}
                  alt={item.label}
                  className="w-full h-full object-contain transition-all duration-200"
                  style={{
                    filter: (() => {
                      // Mining图标特殊处理
                      if (item.route === Routes.MINING) {
                        return isActive
                          ? 'none' // Mining选中状态保持原色（彩色）
                          : 'grayscale(1) brightness(0.3) sepia(0.2) hue-rotate(0deg) saturate(0.5)'; // Mining非选中状态强制转换为灰色
                      }
                      // 其他图标的处理
                      return isActive
                        ? 'brightness(0) saturate(0) invert(1)' // 其他图标选中状态显示白色
                        : 'brightness(0) saturate(0) invert(0.31)'; // 其他图标非选中状态显示灰色
                    })(),
                    opacity: 1
                  }}
                />
              </div>

              {/* 文字标签 */}
              <span
                className={`text-[10px] font-normal text-center transition-colors duration-200 ${
                  isActive ? 'text-white' : 'text-[#4E4F53]'
                }`}
                style={{
                  fontFamily: 'PingFang SC, sans-serif',
                  lineHeight: '1em'
                }}
              >
                {item.label}
              </span>
            </button>
          );
        })}
      </div>
    </nav>
  );
};
