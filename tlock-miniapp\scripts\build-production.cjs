#!/usr/bin/env node

/**
 * Tlock 生产环境构建脚本
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const PRODUCTION_URL = 'https://tg.tlock.org';

function runCommand(command, description) {
  console.log(`\n🔧 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} 完成`);
    return true;
  } catch (error) {
    console.log(`❌ ${description} 失败:`, error.message);
    return false;
  }
}

function updateBotConfig() {
  console.log(`\n🤖 更新机器人配置为生产环境...`);
  console.log(`🌐 生产域名: ${PRODUCTION_URL}`);
  
  try {
    execSync(`node scripts/update-bot-config.cjs ${PRODUCTION_URL}`, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.log('❌ 更新机器人配置失败:', error.message);
    return false;
  }
}

function checkDistFolder() {
  const distPath = path.join(process.cwd(), 'dist');
  if (fs.existsSync(distPath)) {
    console.log('✅ dist 文件夹已生成');
    
    // 显示构建文件信息
    const files = fs.readdirSync(distPath);
    console.log('\n📁 构建文件列表:');
    files.forEach(file => {
      const filePath = path.join(distPath, file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024).toFixed(2);
      console.log(`   ${file} (${size} KB)`);
    });
    
    return true;
  } else {
    console.log('❌ dist 文件夹未找到');
    return false;
  }
}

async function main() {
  console.log('🚀 Tlock 生产环境构建\n');
  console.log(`🎯 目标域名: ${PRODUCTION_URL}`);
  
  // 1. 跳过类型检查，直接构建（生产环境优先）
  console.log('\n⚠️ 跳过类型检查，直接构建生产版本...');

  // 2. 构建应用
  if (!runCommand('npm run build', '构建应用')) {
    process.exit(1);
  }
  
  // 4. 检查构建结果
  if (!checkDistFolder()) {
    process.exit(1);
  }
  
  // 5. 更新机器人配置
  if (!updateBotConfig()) {
    process.exit(1);
  }
  
  console.log('\n🎉 生产环境构建完成！');
  console.log('\n📋 部署步骤：');
  console.log('1. 将 dist/ 文件夹内容上传到服务器');
  console.log(`2. 确保域名 ${PRODUCTION_URL} 指向正确的服务器`);
  console.log('3. 配置 HTTPS 证书');
  console.log('4. 测试机器人功能');
  
  console.log('\n🔗 邀请链接格式：');
  console.log('https://t.me/HabbyBabyBot?startapp=ref_INVITECODE');
  
  console.log('\n📱 测试清单：');
  console.log('- [ ] 访问 https://tg.tlock.org 正常加载');
  console.log('- [ ] 机器人菜单按钮正常工作');
  console.log('- [ ] 邀请链接正常工作');
  console.log('- [ ] 登录功能正常');
  console.log('- [ ] API 调用正常');
}

main().catch(error => {
  console.error('❌ 构建过程中发生错误:', error);
  process.exit(1);
});
