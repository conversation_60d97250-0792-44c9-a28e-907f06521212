#!/bin/bash

# Tlock Bot 后台启动脚本

# 自动检测脚本所在目录的父目录作为 BOT_DIR
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$BOT_DIR/logs"
PID_FILE="$BOT_DIR/bot.pid"
LOG_FILE="$LOG_DIR/bot.log"
ERROR_LOG="$LOG_DIR/bot_error.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 检查是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "❌ Bot 已经在运行 (PID: $PID)"
        exit 1
    else
        echo "⚠️ 发现僵尸PID文件，正在清理..."
        rm -f "$PID_FILE"
    fi
fi

echo "🚀 启动 Tlock Bot..."

# 切换到机器人目录
cd "$BOT_DIR"

# 后台启动机器人
nohup dart run bin/teledart_bot.dart > "$LOG_FILE" 2> "$ERROR_LOG" &

# 保存PID
echo $! > "$PID_FILE"

echo "✅ Bot 已启动"
echo "📋 PID: $(cat $PID_FILE)"
echo "� Bot 目录: $BOT_DIR"
echo "�📄 日志文件: $LOG_FILE"
echo "❌ 错误日志: $ERROR_LOG"
echo ""
echo "📝 常用命令:"
echo "  查看日志: tail -f $LOG_FILE"
echo "  查看错误: tail -f $ERROR_LOG"
echo "  停止服务: $BOT_DIR/scripts/stop-bot.sh"
echo "  重启服务: $BOT_DIR/scripts/restart-bot.sh"
echo "  查看状态: $BOT_DIR/scripts/status-bot.sh"
