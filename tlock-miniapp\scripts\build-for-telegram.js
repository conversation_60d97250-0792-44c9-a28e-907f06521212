#!/usr/bin/env node

/**
 * Build script for Telegram Mini App deployment
 * This script prepares the app for production deployment with Telegram-specific optimizations
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Building Tlock for Telegram Mini App...\n');

// 1. Clean previous build
console.log('📁 Cleaning previous build...');
try {
  execSync('rm -rf dist', { stdio: 'inherit' });
} catch (error) {
  // Directory might not exist, that's ok
}

// 2. Set production environment
process.env.NODE_ENV = 'production';
console.log('🔧 Setting production environment...');

// 3. Build the app
console.log('🔨 Building the application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// 4. Optimize for Telegram
console.log('⚡ Optimizing for Telegram Mini App...');

// Read the built index.html
const indexPath = path.join(__dirname, '../dist/index.html');
let indexContent = fs.readFileSync(indexPath, 'utf8');

// Add Telegram-specific meta tags if not present
const telegramMeta = `
  <!-- Telegram Mini App Optimizations -->
  <meta name="telegram-web-app" content="true" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="mobile-web-app-capable" content="yes" />
`;

// Insert meta tags before closing head tag if not already present
if (!indexContent.includes('telegram-web-app')) {
  indexContent = indexContent.replace('</head>', `${telegramMeta}</head>`);
  fs.writeFileSync(indexPath, indexContent);
}

// 5. Create deployment info
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
const deploymentInfo = {
  buildTime: new Date().toISOString(),
  version: packageJson.version,
  environment: 'production',
  telegramOptimized: true,
  features: [
    'Telegram Web App Integration',
    'Haptic Feedback',
    'Theme Adaptation',
    'User Data Integration',
    'Touch Optimizations'
  ]
};

fs.writeFileSync(
  path.join(__dirname, '../dist/deployment-info.json'),
  JSON.stringify(deploymentInfo, null, 2)
);

// 6. Generate deployment checklist
const checklist = `
# Telegram Mini App Deployment Checklist

## ✅ Pre-deployment
- [x] Application built successfully
- [x] Telegram Web App script included
- [x] HTTPS required for production
- [x] Meta tags optimized for mobile
- [x] Touch interactions optimized

## 🔧 Bot Configuration Required
- [ ] Create bot with @BotFather
- [ ] Set Mini App URL in @BotFather
- [ ] Upload bot icon (512x512 PNG)
- [ ] Set bot description
- [ ] Configure menu button (optional)

## 🌐 Server Configuration Required
- [ ] HTTPS certificate installed
- [ ] Domain configured (e.g., tlock.xyz)
- [ ] CORS headers configured if needed
- [ ] Gzip compression enabled
- [ ] Static file caching configured

## 📱 Testing Required
- [ ] Test in Telegram mobile app
- [ ] Test user data retrieval
- [ ] Test haptic feedback
- [ ] Test theme adaptation
- [ ] Test all navigation flows
- [ ] Test API integration

## 🚀 Go Live
- [ ] Update bot Mini App URL to production domain
- [ ] Test with real users
- [ ] Monitor error logs
- [ ] Monitor performance metrics

Build completed at: ${new Date().toISOString()}
Version: ${deploymentInfo.version}
`;

fs.writeFileSync(
  path.join(__dirname, '../dist/DEPLOYMENT_CHECKLIST.md'),
  checklist
);

// 7. Show build summary
console.log('\n✅ Build completed successfully!');
console.log('\n📊 Build Summary:');
console.log(`   Version: ${deploymentInfo.version}`);
console.log(`   Build Time: ${deploymentInfo.buildTime}`);
console.log(`   Output Directory: dist/`);

console.log('\n📋 Next Steps:');
console.log('   1. Upload dist/ folder to your HTTPS server');
console.log('   2. Configure your Telegram bot with @BotFather');
console.log('   3. Set the Mini App URL to your domain');
console.log('   4. Test the integration in Telegram');
console.log('\n📖 See dist/DEPLOYMENT_CHECKLIST.md for detailed steps');

console.log('\n🎉 Ready for Telegram Mini App deployment!');
