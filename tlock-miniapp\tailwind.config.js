/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Telegram theme colors
        primary: {
          50: '#eff9ff',
          100: '#dff2ff',
          200: '#b8e7ff',
          300: '#78d6ff',
          400: '#30c2ff',
          500: '#259aee', // Main blue from Figma
          600: '#0d7bc7',
          700: '#0e62a1',
          800: '#125385',
          900: '#15456e',
        },
        // Dark theme colors
        dark: {
          bg: '#171717',
          surface: '#202428',
          border: '#1a1a1a',
          text: {
            primary: '#ffffff',
            secondary: '#a1a1aa',
            muted: '#71717a',
          }
        },
        // Light theme colors
        light: {
          bg: '#ffffff',
          surface: '#f8fafc',
          border: '#e2e8f0',
          text: {
            primary: '#0f172a',
            secondary: '#475569',
            muted: '#64748b',
          }
        },
        // Status colors
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#3b82f6',
        // Gradient colors from Figma
        gradient: {
          blue: '#259AEE',
          cyan: '#7AFFFF',
        }
      },
      fontFamily: {
        sans: [
          'HarmonyOS Sans SC',
          'PingFang SC',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'sans-serif'
        ],
      },
      fontSize: {
        'xs': ['12px', { lineHeight: '16px' }],
        'sm': ['14px', { lineHeight: '20px' }],
        'base': ['16px', { lineHeight: '24px' }],
        'lg': ['18px', { lineHeight: '28px' }],
        'xl': ['20px', { lineHeight: '28px' }],
        '2xl': ['24px', { lineHeight: '32px' }],
        '3xl': ['30px', { lineHeight: '36px' }],
        '4xl': ['36px', { lineHeight: '40px' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        'xl': '12px',
        '2xl': '16px',
        '3xl': '24px',
      },
      boxShadow: {
        'soft': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'medium': '0 4px 16px rgba(0, 0, 0, 0.15)',
        'strong': '0 8px 32px rgba(0, 0, 0, 0.2)',
        'telegram': '0 2px 8px rgba(37, 154, 238, 0.2)',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-gentle': 'bounceGentle 1s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },
      screens: {
        'xs': '375px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      zIndex: {
        'modal': '1000',
        'dropdown': '100',
        'sticky': '10',
        'overlay': '50',
      },
    },
  },
  plugins: [
    // Custom plugin for Telegram-specific utilities
    function({ addUtilities, theme }) {
      const newUtilities = {
        '.safe-area-top': {
          paddingTop: 'env(safe-area-inset-top)',
        },
        '.safe-area-bottom': {
          paddingBottom: 'env(safe-area-inset-bottom)',
        },
        '.safe-area-left': {
          paddingLeft: 'env(safe-area-inset-left)',
        },
        '.safe-area-right': {
          paddingRight: 'env(safe-area-inset-right)',
        },
        '.telegram-viewport': {
          height: 'var(--tg-viewport-height, 100vh)',
        },
        '.telegram-stable-viewport': {
          height: 'var(--tg-viewport-stable-height, 100vh)',
        },
        '.touch-target': {
          minHeight: '44px',
          minWidth: '44px',
        },
        '.gradient-primary': {
          background: `linear-gradient(135deg, ${theme('colors.gradient.blue')} 0%, ${theme('colors.gradient.cyan')} 100%)`,
        },
      };
      addUtilities(newUtilities);
    },
  ],
};
