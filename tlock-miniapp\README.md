# Tlock Telegram Mini App

A decentralized social platform built as a Telegram Mini App with mining mechanics, daily rewards, and social features.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- pnpm 8+ (recommended package manager)
- Modern browser with Telegram Web App support

### Installation

```bash
# Clone the repository
git clone https://github.com/tlock/tlock-miniapp.git
cd tlock-miniapp

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

### Development

```bash
# Start development server
pnpm dev

# Run tests
pnpm test

# Run tests with UI
pnpm test:ui

# Run tests with coverage
pnpm test:coverage

# Lint code
pnpm lint

# Fix linting issues
pnpm lint:fix

# Format code
pnpm format

# Type check
pnpm type-check

# Build for production
pnpm build

# Preview production build
pnpm preview
```

## 🏗️ Tech Stack

- **Frontend Framework**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **HTTP Client**: Axios + React Query
- **UI Components**: Headless UI + Custom Components
- **Testing**: Vitest + React Testing Library
- **Linting**: ESLint + Prettier
- **PWA**: Vite PWA Plugin

## 📱 Features

### Core Features
- ✅ User authentication via Telegram
- ✅ Mining mechanics with real-time updates
- ✅ Daily reward system
- ✅ Social features (friends, chat)
- ✅ Level progression system
- ✅ Task completion system
- ✅ Invite friends functionality

### Technical Features
- ✅ Mobile-first responsive design
- ✅ Dark/Light theme support
- ✅ PWA capabilities
- ✅ Offline support
- ✅ Real-time updates via WebSocket
- ✅ Performance optimized
- ✅ TypeScript for type safety

## 📂 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components
│   ├── layout/         # Layout components
│   └── features/       # Feature-specific components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── services/           # API services and Telegram integration
├── store/              # Zustand store slices
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
├── styles/             # Global styles and theme
├── assets/             # Static assets
└── constants/          # Application constants
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=https://api.tlock.xyz
VITE_TELEGRAM_BOT_TOKEN=your_bot_token_here
VITE_APP_ENV=development
```

### Telegram Bot Setup

1. Create a bot with [@BotFather](https://t.me/botfather)
2. Set up Mini App URL in bot settings
3. Configure bot token in environment variables
4. Set up webhook for production

## 📱 Telegram Integration

### Web App Initialization

```typescript
import { initTelegramWebApp } from '@/services/telegram';

// Initialize Telegram Web App
const tg = initTelegramWebApp();

// Access user data
const user = tg.initDataUnsafe.user;

// Handle theme changes
tg.onEvent('themeChanged', () => {
  // Update app theme
});
```

### Available Telegram Features

- User authentication and data
- Theme integration (light/dark)
- Main button control
- Back button handling
- Haptic feedback
- Cloud storage
- Payment integration

## 🎨 Design System

### Colors

- **Primary**: #259AEE (Telegram blue)
- **Secondary**: #7AFFFF (Cyan accent)
- **Background**: #171717 (Dark) / #FFFFFF (Light)
- **Surface**: #202428 (Dark) / #F8FAFC (Light)

### Typography

- **Primary Font**: HarmonyOS Sans SC
- **Fallback**: PingFang SC, system fonts

### Components

All components follow mobile-first design principles with:
- Minimum touch target size: 44px
- Smooth 60fps animations
- Proper accessibility support
- Telegram theme integration

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test --watch

# Run specific test file
pnpm test UserProfile.test.tsx
```

### Test Structure

```typescript
// Example test file
import { render, screen } from '@testing-library/react';
import { UserProfile } from '@/components/features/UserProfile';

describe('UserProfile', () => {
  it('should render user information correctly', () => {
    render(<UserProfile userId="123" />);
    expect(screen.getByText('User Profile')).toBeInTheDocument();
  });
});
```

## 🚀 Deployment

### Build for Production

```bash
# Create production build
pnpm build

# Preview production build locally
pnpm preview
```

### Deployment Checklist

- [ ] Environment variables configured
- [ ] HTTPS enabled for Telegram compatibility
- [ ] Service worker configured
- [ ] Bundle size optimized (<1MB gzipped)
- [ ] Performance metrics validated
- [ ] Telegram Bot configured with correct URL

## 📊 Performance

### Bundle Size Targets

- **Total bundle**: <1MB gzipped
- **Initial load**: <500KB gzipped
- **Code splitting**: Implemented for routes and features

### Performance Metrics

- **First Contentful Paint**: <1.5s
- **Largest Contentful Paint**: <2.5s
- **Cumulative Layout Shift**: <0.1
- **First Input Delay**: <100ms

## 🔒 Security

### Data Validation

All data from Telegram Web App is validated:

```typescript
// Validate Telegram data
const isValidTelegramData = validateTelegramWebAppData(
  window.Telegram.WebApp.initData
);
```

### Best Practices

- Never trust client-side data for sensitive operations
- Validate all API inputs on backend
- Use HTTPS in production
- Implement proper error handling
- Sanitize user inputs

## 🤝 Contributing

### Development Rules

1. **English Only**: All code, comments, and documentation must be in English
2. **Mobile First**: Always design for mobile devices first
3. **Performance**: Optimize for fast loading and smooth animations
4. **Testing**: Write tests for all new features
5. **Type Safety**: Use TypeScript for all code

### Code Style

- Use ESLint and Prettier for consistent formatting
- Follow React best practices
- Use semantic commit messages
- Write clear, descriptive variable names

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit pull request with clear description

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs via GitHub Issues
- **Telegram**: Join our development channel
- **Email**: <EMAIL>

---

**Built with ❤️ for the Telegram ecosystem**
