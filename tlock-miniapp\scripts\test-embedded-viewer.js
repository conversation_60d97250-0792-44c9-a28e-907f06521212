#!/usr/bin/env node

/**
 * 测试内嵌文档查看器的脚本
 * 用于在开发环境中快速测试正式环境的行为
 */

const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '..', '.env');

function enableEmbeddedViewer() {
  const envContent = 'VITE_FORCE_EMBEDDED_VIEWER=true\n';
  fs.writeFileSync(envPath, envContent);
  console.log('✅ 已启用内嵌文档查看器模式');
  console.log('🔄 请重启开发服务器以应用更改');
}

function disableEmbeddedViewer() {
  const envContent = 'VITE_FORCE_EMBEDDED_VIEWER=false\n';
  fs.writeFileSync(envPath, envContent);
  console.log('✅ 已禁用内嵌文档查看器模式（使用外部链接）');
  console.log('🔄 请重启开发服务器以应用更改');
}

function showStatus() {
  if (fs.existsSync(envPath)) {
    const content = fs.readFileSync(envPath, 'utf8');
    const isEnabled = content.includes('VITE_FORCE_EMBEDDED_VIEWER=true');
    console.log(`📊 当前状态: ${isEnabled ? '内嵌查看器模式' : '外部链接模式'}`);
  } else {
    console.log('📊 当前状态: 外部链接模式（默认）');
  }
}

const command = process.argv[2];

switch (command) {
  case 'enable':
    enableEmbeddedViewer();
    break;
  case 'disable':
    disableEmbeddedViewer();
    break;
  case 'status':
    showStatus();
    break;
  default:
    console.log('📖 用法:');
    console.log('  node scripts/test-embedded-viewer.js enable   # 启用内嵌查看器');
    console.log('  node scripts/test-embedded-viewer.js disable  # 禁用内嵌查看器');
    console.log('  node scripts/test-embedded-viewer.js status   # 查看当前状态');
    break;
}
