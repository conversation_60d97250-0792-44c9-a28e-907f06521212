#!/bin/bash

# 设置脚本权限和配置

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🔧 设置 Tlock Bot 管理脚本..."
echo "📁 Bot 目录: $BOT_DIR"

# 设置脚本权限
chmod +x "$SCRIPT_DIR"/*.sh
echo "✅ 脚本权限已设置"

# 更新脚本中的路径
echo "📝 更新脚本路径配置..."

for script in start-bot.sh stop-bot.sh restart-bot.sh status-bot.sh manage-logs.sh; do
    if [ -f "$SCRIPT_DIR/$script" ]; then
        sed -i "s|/path/to/your/tlockdart|$BOT_DIR|g" "$SCRIPT_DIR/$script"
        echo "  ✅ 已更新 $script"
    fi
done

# 更新 systemd 服务文件
if [ -f "$SCRIPT_DIR/tlock-bot.service" ]; then
    sed -i "s|/path/to/your/tlockdart|$BOT_DIR|g" "$SCRIPT_DIR/tlock-bot.service"
    sed -i "s|your-username|$(whoami)|g" "$SCRIPT_DIR/tlock-bot.service"
    sed -i "s|your-group|$(id -gn)|g" "$SCRIPT_DIR/tlock-bot.service"
    echo "  ✅ 已更新 systemd 服务文件"
fi

# 创建必要的目录
mkdir -p "$BOT_DIR/logs"
mkdir -p "$BOT_DIR/data"
echo "✅ 目录结构已创建"

echo ""
echo "🎉 设置完成！"
echo ""
echo "📋 可用命令:"
echo "  启动服务: $SCRIPT_DIR/start-bot.sh"
echo "  停止服务: $SCRIPT_DIR/stop-bot.sh"
echo "  重启服务: $SCRIPT_DIR/restart-bot.sh"
echo "  查看状态: $SCRIPT_DIR/status-bot.sh"
echo "  管理日志: $SCRIPT_DIR/manage-logs.sh"
echo ""
echo "🔧 systemd 服务安装 (可选):"
echo "  sudo cp $SCRIPT_DIR/tlock-bot.service /etc/systemd/system/"
echo "  sudo systemctl daemon-reload"
echo "  sudo systemctl enable tlock-bot"
echo "  sudo systemctl start tlock-bot"
