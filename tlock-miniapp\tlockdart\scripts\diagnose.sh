#!/bin/bash

# Tlock Bot 诊断脚本

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🔍 Tlock Bot 诊断报告"
echo "=" * 50
echo "📁 Bot 目录: $BOT_DIR"
echo "📁 脚本目录: $SCRIPT_DIR"
echo ""

# 1. 检查目录结构
echo "1️⃣ 目录结构检查:"
echo "   Bot 目录存在: $([ -d "$BOT_DIR" ] && echo "✅" || echo "❌")"
echo "   脚本目录存在: $([ -d "$SCRIPT_DIR" ] && echo "✅" || echo "❌")"
echo "   主程序文件: $([ -f "$BOT_DIR/bin/teledart_bot.dart" ] && echo "✅" || echo "❌")"
echo "   日志目录: $([ -d "$BOT_DIR/logs" ] && echo "✅" || echo "❌")"
echo "   数据目录: $([ -d "$BOT_DIR/data" ] && echo "✅" || echo "❌")"
echo ""

# 2. 检查进程状态
echo "2️⃣ 进程状态检查:"
PID_FILE="$BOT_DIR/bot.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    echo "   PID 文件存在: ✅ (PID: $PID)"
    
    if ps -p $PID > /dev/null 2>&1; then
        echo "   进程运行状态: ✅ 正在运行"
        echo "   进程信息:"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem | sed 's/^/     /'
    else
        echo "   进程运行状态: ❌ 进程不存在"
    fi
else
    echo "   PID 文件存在: ❌"
fi

# 检查所有 dart 进程
echo ""
echo "   所有 Dart 进程:"
ps aux | grep dart | grep -v grep | sed 's/^/     /' || echo "     无 Dart 进程运行"
echo ""

# 3. 检查日志文件
echo "3️⃣ 日志文件检查:"
LOG_DIR="$BOT_DIR/logs"
if [ -d "$LOG_DIR" ]; then
    echo "   日志目录存在: ✅"
    
    LOG_FILE="$LOG_DIR/bot.log"
    ERROR_LOG="$LOG_DIR/bot_error.log"
    
    if [ -f "$LOG_FILE" ]; then
        LOG_SIZE=$(du -h "$LOG_FILE" | cut -f1)
        LOG_LINES=$(wc -l < "$LOG_FILE")
        echo "   普通日志: ✅ ($LOG_SIZE, $LOG_LINES 行)"
        echo "   最近日志:"
        tail -5 "$LOG_FILE" | sed 's/^/     /'
    else
        echo "   普通日志: ❌ 文件不存在"
    fi
    
    if [ -f "$ERROR_LOG" ]; then
        ERROR_SIZE=$(du -h "$ERROR_LOG" | cut -f1)
        ERROR_LINES=$(wc -l < "$ERROR_LOG")
        echo "   错误日志: ⚠️ ($ERROR_SIZE, $ERROR_LINES 行)"
        if [ -s "$ERROR_LOG" ]; then
            echo "   最近错误:"
            tail -5 "$ERROR_LOG" | sed 's/^/     /'
        fi
    else
        echo "   错误日志: ✅ 无错误日志"
    fi
else
    echo "   日志目录存在: ❌"
fi
echo ""

# 4. 检查环境
echo "4️⃣ 环境检查:"
echo "   Dart 安装: $(command -v dart >/dev/null && echo "✅ $(dart --version 2>&1)" || echo "❌ 未安装")"
echo "   当前用户: $(whoami)"
echo "   当前目录: $(pwd)"
echo "   Bot Token: $([ -n "$TELEGRAM_BOT_TOKEN" ] && echo "✅ 已设置" || echo "⚠️ 未设置环境变量")"
echo ""

# 5. 检查网络连接
echo "5️⃣ 网络连接检查:"
if command -v curl >/dev/null; then
    if curl -s --connect-timeout 5 https://api.telegram.org >/dev/null; then
        echo "   Telegram API: ✅ 可访问"
    else
        echo "   Telegram API: ❌ 无法访问"
    fi
else
    echo "   网络检查: ⚠️ curl 未安装"
fi
echo ""

# 6. 检查文件权限
echo "6️⃣ 文件权限检查:"
echo "   脚本权限:"
for script in start-bot.sh stop-bot.sh restart-bot.sh status-bot.sh; do
    if [ -f "$SCRIPT_DIR/$script" ]; then
        PERM=$(ls -l "$SCRIPT_DIR/$script" | cut -d' ' -f1)
        EXEC=$([ -x "$SCRIPT_DIR/$script" ] && echo "✅" || echo "❌")
        echo "     $script: $EXEC ($PERM)"
    fi
done
echo ""

# 7. 建议操作
echo "7️⃣ 建议操作:"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "   🔧 清理僵尸 PID 文件: rm $PID_FILE"
    fi
fi

if [ ! -d "$BOT_DIR/logs" ]; then
    echo "   🔧 创建日志目录: mkdir -p $BOT_DIR/logs"
fi

if [ ! -x "$SCRIPT_DIR/start-bot.sh" ]; then
    echo "   🔧 设置脚本权限: chmod +x $SCRIPT_DIR/*.sh"
fi

if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
    echo "   🔧 设置环境变量: export TELEGRAM_BOT_TOKEN='your_token'"
fi

echo ""
echo "🎯 快速命令:"
echo "   查看实时日志: tail -f $BOT_DIR/logs/bot.log"
echo "   重启服务: $SCRIPT_DIR/restart-bot.sh"
echo "   查看状态: $SCRIPT_DIR/status-bot.sh"
