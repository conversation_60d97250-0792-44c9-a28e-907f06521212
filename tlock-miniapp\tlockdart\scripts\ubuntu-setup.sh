#!/bin/bash

# Ubuntu 服务器快速设置脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}🚀 Tlock Bot Ubuntu 服务器设置${NC}"
echo -e "${BLUE}📁 Bot 目录: $BOT_DIR${NC}"
echo ""

# 1. 检查系统环境
echo -e "${YELLOW}1️⃣ 检查系统环境...${NC}"

# 检查是否为 Ubuntu
if ! grep -q "Ubuntu" /etc/os-release; then
    echo -e "${RED}❌ 警告: 此脚本专为 Ubuntu 设计${NC}"
fi

# 检查 Dart 是否安装
if ! command -v dart &> /dev/null; then
    echo -e "${RED}❌ Dart 未安装${NC}"
    echo -e "${YELLOW}📥 正在安装 Dart...${NC}"
    
    # 安装 Dart
    sudo apt-get update
    sudo apt-get install -y apt-transport-https
    wget -qO- https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo gpg --dearmor -o /usr/share/keyrings/dart.gpg
    echo 'deb [signed-by=/usr/share/keyrings/dart.gpg arch=amd64] https://storage.googleapis.com/download.dartlang.org/linux/debian stable main' | sudo tee /etc/apt/sources.list.d/dart_stable.list
    sudo apt-get update
    sudo apt-get install -y dart
    
    # 添加到 PATH
    echo 'export PATH="$PATH:/usr/lib/dart/bin"' >> ~/.bashrc
    export PATH="$PATH:/usr/lib/dart/bin"
    
    echo -e "${GREEN}✅ Dart 安装完成${NC}"
else
    echo -e "${GREEN}✅ Dart 已安装: $(dart --version)${NC}"
fi

# 2. 设置目录权限
echo -e "${YELLOW}2️⃣ 设置目录权限...${NC}"

# 设置脚本执行权限
chmod +x "$SCRIPT_DIR"/*.sh
echo -e "${GREEN}✅ 脚本权限已设置${NC}"

# 创建必要目录
mkdir -p "$BOT_DIR/logs"
mkdir -p "$BOT_DIR/data"
echo -e "${GREEN}✅ 目录结构已创建${NC}"

# 3. 检查依赖
echo -e "${YELLOW}3️⃣ 检查项目依赖...${NC}"

cd "$BOT_DIR"
if [ -f "pubspec.yaml" ]; then
    echo -e "${BLUE}📦 安装 Dart 依赖...${NC}"
    dart pub get
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
else
    echo -e "${RED}❌ 未找到 pubspec.yaml 文件${NC}"
    exit 1
fi

# 4. 检查环境变量
echo -e "${YELLOW}4️⃣ 检查环境变量...${NC}"

if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
    echo -e "${YELLOW}⚠️ 未设置 TELEGRAM_BOT_TOKEN 环境变量${NC}"
    echo -e "${BLUE}💡 建议在 ~/.bashrc 中添加:${NC}"
    echo -e "${BLUE}   export TELEGRAM_BOT_TOKEN='your_bot_token'${NC}"
else
    echo -e "${GREEN}✅ TELEGRAM_BOT_TOKEN 已设置${NC}"
fi

# 5. 测试机器人代码
echo -e "${YELLOW}5️⃣ 测试机器人代码...${NC}"

if dart analyze bin/teledart_bot.dart > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 代码分析通过${NC}"
else
    echo -e "${RED}❌ 代码分析发现问题${NC}"
    dart analyze bin/teledart_bot.dart
fi

# 6. 创建 systemd 服务（可选）
echo -e "${YELLOW}6️⃣ 创建 systemd 服务...${NC}"

SERVICE_FILE="/etc/systemd/system/tlock-bot.service"
if [ ! -f "$SERVICE_FILE" ]; then
    echo -e "${BLUE}📝 创建 systemd 服务文件...${NC}"
    
    sudo tee "$SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=Tlock Telegram Bot Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=$(whoami)
Group=$(id -gn)
WorkingDirectory=$BOT_DIR
ExecStart=/usr/lib/dart/bin/dart run bin/teledart_bot.dart
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=NODE_ENV=production
Environment=TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-**********************************************}

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$BOT_DIR/data
ReadWritePaths=$BOT_DIR/logs

# 资源限制
LimitNOFILE=65536
MemoryMax=512M

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    echo -e "${GREEN}✅ systemd 服务已创建${NC}"
    echo -e "${BLUE}💡 使用以下命令管理服务:${NC}"
    echo -e "${BLUE}   sudo systemctl enable tlock-bot  # 开机自启${NC}"
    echo -e "${BLUE}   sudo systemctl start tlock-bot   # 启动服务${NC}"
    echo -e "${BLUE}   sudo systemctl status tlock-bot  # 查看状态${NC}"
else
    echo -e "${GREEN}✅ systemd 服务已存在${NC}"
fi

# 7. 显示管理命令
echo ""
echo -e "${GREEN}🎉 设置完成！${NC}"
echo ""
echo -e "${BLUE}📋 管理命令:${NC}"
echo -e "${BLUE}  启动服务: $SCRIPT_DIR/start-bot.sh${NC}"
echo -e "${BLUE}  停止服务: $SCRIPT_DIR/stop-bot.sh${NC}"
echo -e "${BLUE}  重启服务: $SCRIPT_DIR/restart-bot.sh${NC}"
echo -e "${BLUE}  查看状态: $SCRIPT_DIR/status-bot.sh${NC}"
echo -e "${BLUE}  管理日志: $SCRIPT_DIR/manage-logs.sh${NC}"
echo ""
echo -e "${BLUE}🔧 systemd 管理 (推荐):${NC}"
echo -e "${BLUE}  sudo systemctl start tlock-bot${NC}"
echo -e "${BLUE}  sudo systemctl stop tlock-bot${NC}"
echo -e "${BLUE}  sudo systemctl restart tlock-bot${NC}"
echo -e "${BLUE}  sudo systemctl status tlock-bot${NC}"
echo -e "${BLUE}  sudo journalctl -u tlock-bot -f${NC}"
echo ""
echo -e "${YELLOW}⚠️ 重要提醒:${NC}"
echo -e "${YELLOW}  - 确保设置了正确的 TELEGRAM_BOT_TOKEN${NC}"
echo -e "${YELLOW}  - 定期检查日志输出${NC}"
echo -e "${YELLOW}  - 监控服务运行状态${NC}"
