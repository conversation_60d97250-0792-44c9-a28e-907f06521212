import React, { useState, useEffect } from 'react';
import { InviteService } from '../services/inviteService';

export const BotTestPage: React.FC = () => {
  const [botInfo, setBotInfo] = useState<any>(null);
  const [inviteLink, setInviteLink] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testBotInfo = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 设置一个测试邀请码
      InviteService.setInvitationCode('TESTCODE123');
      
      // 生成邀请链接（这会触发机器人信息获取）
      const link = await InviteService.generateInviteLink();
      setInviteLink(link);
      
      // 直接调用Telegram Bot API获取机器人信息
      const response = await fetch('https://api.telegram.org/bot8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk/getMe');
      const data = await response.json();
      setBotInfo(data);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testBotInfo();
  }, []);

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">机器人信息测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={testBotInfo}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-500 text-white px-4 py-2 rounded-lg"
          >
            {loading ? '测试中...' : '重新测试'}
          </button>

          {error && (
            <div className="bg-red-900 border border-red-500 rounded-lg p-4">
              <h3 className="text-red-400 font-bold mb-2">错误</h3>
              <p className="text-red-300">{error}</p>
            </div>
          )}

          {botInfo && (
            <div className="bg-gray-900 rounded-lg p-4">
              <h3 className="text-green-400 font-bold mb-2">机器人信息</h3>
              <pre className="text-sm text-gray-300 overflow-auto">
                {JSON.stringify(botInfo, null, 2)}
              </pre>
            </div>
          )}

          {inviteLink && (
            <div className="bg-gray-900 rounded-lg p-4">
              <h3 className="text-blue-400 font-bold mb-2">生成的邀请链接</h3>
              <p className="text-blue-300 break-all">{inviteLink}</p>
              <button
                onClick={() => navigator.clipboard.writeText(inviteLink)}
                className="mt-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
              >
                复制链接
              </button>
            </div>
          )}

          <div className="bg-gray-900 rounded-lg p-4">
            <h3 className="text-yellow-400 font-bold mb-2">说明</h3>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• 这个页面测试动态获取机器人用户名的功能</li>
              <li>• 如果API调用成功，邀请链接会使用实际的机器人用户名</li>
              <li>• 如果API调用失败，会使用默认的用户名 'HabbyBabyBot'</li>
              <li>• 检查生成的邀请链接是否包含正确的机器人用户名</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
