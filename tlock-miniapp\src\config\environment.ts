// 环境配置
export const ENV_CONFIG = {
  // 是否为正式环境（小程序环境）
  IS_PRODUCTION: import.meta.env.MODE === 'production',

  // 是否强制使用内嵌文档查看器（用于开发时测试）
  FORCE_EMBEDDED_VIEWER: import.meta.env['VITE_FORCE_EMBEDDED_VIEWER'] === 'true',
  
  // 文档配置
  DOCUMENT: {
    url: 'https://tlock.gitbook.io/tlock',
    title: 'Tlock Documentation'
  }
};

// 判断是否应该使用内嵌文档查看器
export const shouldUseEmbeddedViewer = () => {
  // 在小程序环境中使用Telegram Web App API打开链接
  return true;
};
