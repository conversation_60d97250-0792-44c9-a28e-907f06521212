/**
 * 用户相关的工具函数
 */

/**
 * 安全获取用户名，处理null、"null"字符串和空字符串的情况
 * @param username 原始用户名
 * @param fallback 默认值
 * @returns 处理后的用户名
 */
export const getSafeUsername = (username: string | null | undefined, fallback: string = 'Guest'): string => {
  // 检查是否为有效的用户名
  if (username && 
      username !== 'null' && 
      username.trim() !== '' && 
      username.toLowerCase() !== 'undefined') {
    return username.trim();
  }
  return fallback;
};

/**
 * 检查用户名是否有效
 * @param username 用户名
 * @returns 是否有效
 */
export const isValidUsername = (username: string | null | undefined): boolean => {
  return !!(username && 
           username !== 'null' && 
           username.trim() !== '' && 
           username.toLowerCase() !== 'undefined');
}; 