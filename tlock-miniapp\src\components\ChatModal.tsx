import React, { useState } from 'react';

interface ChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSendMessage: (message: string) => void;
  recentMessages: string[]; // 最新的5条消息用于预设
}

export const ChatModal: React.FC<ChatModalProps> = ({
  isOpen,
  onClose,
  onSendMessage,
  recentMessages
}) => {
  const [customMessage, setCustomMessage] = useState('');

  if (!isOpen) return null;

  const handleSendPreset = (message: string) => {
    onSendMessage(message);
    onClose();
  };

  const handleSendCustom = () => {
    if (customMessage.trim()) {
      onSendMessage(customMessage.trim());
      setCustomMessage('');
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-end justify-center z-50">
      <div
        className="w-full mx-4 mb-4 rounded-t-3xl border-t"
        style={{
          backgroundColor: '#13171C',
          borderColor: '#1A1A1A'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <h3
            className="text-xl font-bold text-center flex-1 text-white"
            style={{ fontFamily: 'HarmonyOS Sans SC' }}
          >
            Chat Room
          </h3>
          <button
            onClick={onClose}
            className="w-7 h-7 rounded-full bg-white/20 flex items-center justify-center text-white text-lg"
          >
            ×
          </button>
        </div>

        {/* Preset Messages */}
        <div className="p-4 space-y-3">
          <div className="text-sm text-gray-400 mb-3">Quick messages:</div>

          {recentMessages.map((message, index) => (
            <button
              key={index}
              onClick={() => handleSendPreset(message)}
              className="w-full text-left p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors"
              style={{ backgroundColor: '#0E1114' }}
            >
              <span className="text-sm text-white">{message}</span>
            </button>
          ))}
        </div>

        {/* Custom Message Input */}
        <div className="p-4 border-t border-gray-800">
          <div className="flex space-x-3">
            <div 
              className="flex-1 rounded-lg border"
              style={{ 
                backgroundColor: '#1B1C24',
                borderColor: '#1A1A1A'
              }}
            >
              <input
                type="text"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                placeholder="Type your message..."
                className="w-full px-4 py-3 bg-transparent text-white placeholder-gray-500 outline-none"
                onKeyPress={(e) => e.key === 'Enter' && handleSendCustom()}
              />
            </div>
            
            <button
              onClick={handleSendCustom}
              disabled={!customMessage.trim()}
              className="w-12 h-12 rounded-lg bg-blue-500 disabled:bg-gray-600 disabled:opacity-50 flex items-center justify-center transition-colors"
            >
              <svg 
                width="20" 
                height="20" 
                viewBox="0 0 24 24" 
                fill="none" 
                className="text-white"
              >
                <path 
                  d="M2 21L23 12L2 3V10L17 12L2 14V21Z" 
                  fill="currentColor"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
