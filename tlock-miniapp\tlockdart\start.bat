@echo off
REM TLock Telegram Bot 启动脚本 (Windows)
REM 使用方法: start.bat [dev|prod]

setlocal enabledelayedexpansion

set MODE=%1
if "%MODE%"=="" set MODE=dev

echo [INFO] TLock Telegram Bot 启动脚本
echo [INFO] 模式: %MODE%

REM 检查 Dart 是否安装
dart --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Dart 未安装，请先安装 Dart SDK
    pause
    exit /b 1
)
echo [SUCCESS] Dart 已安装

REM 检查环境变量文件
if not exist ".env" (
    echo [WARNING] .env 文件不存在
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo [INFO] 已复制 .env.example 到 .env，请编辑配置
        echo [INFO] 请编辑 .env 文件设置正确的 TELEGRAM_BOT_TOKEN
        pause
    )
)

REM 安装依赖
echo [INFO] 安装依赖...
dart pub get
if errorlevel 1 (
    echo [ERROR] 依赖安装失败
    pause
    exit /b 1
)
echo [SUCCESS] 依赖安装完成

REM 根据模式运行
if "%MODE%"=="dev" (
    echo [INFO] 以开发模式启动机器人...
    dart run bin/teledart_bot.dart
) else if "%MODE%"=="prod" (
    echo [INFO] 编译可执行文件...
    dart compile exe bin/teledart_bot.dart -o tlock_bot.exe
    if errorlevel 1 (
        echo [ERROR] 编译失败
        pause
        exit /b 1
    )
    echo [SUCCESS] 编译完成
    echo [INFO] 以生产模式启动机器人...
    tlock_bot.exe
) else (
    echo [ERROR] 未知模式: %MODE%
    echo [INFO] 使用方法: %0 [dev^|prod]
    pause
    exit /b 1
)

pause
