import React from 'react';
import { colors, typography, layout } from '../styles/design-system';

interface StatusBarProps {
  time?: string;
  batteryLevel?: number;
  signalStrength?: number;
  showTlockBranding?: boolean;
  title?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
  showDropdownArrow?: boolean;
  showMenuButton?: boolean;
  onMenuClick?: () => void;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  time = '11:00',
  batteryLevel = 27,
  signalStrength = 4,
  showTlockBranding = false,
  title,
  showBackButton = false,
  onBackClick,
  showDropdownArrow = false,
  showMenuButton = false,
  onMenuClick,
}) => {
  return (
    <div
      className="flex items-center justify-between px-4 py-2"
      style={{
        height: layout.headerHeight,
        background: colors.background,
        color: colors.textPrimary,
        fontSize: typography.sm,
        fontFamily: typography.secondary,
        fontWeight: typography.medium,
      }}
    >
      {/* Left Side - Time, Back Button, or Tlock Branding */}
      <div className="flex items-center">
        {showBackButton && onBackClick ? (
          <button
            onClick={onBackClick}
            className="mr-3 p-1 hover:bg-gray-800 rounded transition-colors"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M15 18L9 12L15 6"
                stroke={colors.textPrimary}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        ) : null}

        {title ? (
          <div className="flex items-center">
            <span
              className="font-normal"
              style={{
                fontSize: '21px',
                fontFamily: 'PingFang SC',
              }}
            >
              {title}
            </span>
            {showDropdownArrow && (
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="ml-2">
                <path
                  d="M4 6L8 10L12 6"
                  stroke={colors.textPrimary}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </div>
        ) : showTlockBranding ? (
          <div className="flex items-center space-x-2">
            {/* Tlock Logo Placeholder */}
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center"
              style={{ background: colors.primary }}
            >
              <span className="text-xs font-bold">T</span>
            </div>
            <span
              className="font-bold"
              style={{
                fontSize: '21px',
                fontFamily: typography.secondary,
              }}
            >
              Tlock
            </span>
          </div>
        ) : (
          <span className="font-medium">{time}</span>
        )}
      </div>

      {/* Center - Title if no back button */}
      {title && !showBackButton && (
        <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center">
          <span
            className="font-normal"
            style={{
              fontSize: '21px',
              fontFamily: 'PingFang SC',
            }}
          >
            {title}
          </span>
          {showDropdownArrow && (
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="ml-2">
              <path
                d="M4 6L8 10L12 6"
                stroke={colors.textPrimary}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </div>
      )}

      {/* Right Side - Menu Button and System Status */}
      <div className="flex items-center space-x-1">
        {/* Menu Button (if enabled) */}
        {showMenuButton && onMenuClick && (
          <button
            onClick={onMenuClick}
            className="mr-2 p-1 hover:bg-gray-800 rounded transition-colors"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z"
                fill={colors.textPrimary}
              />
              <path
                d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z"
                fill={colors.textPrimary}
              />
              <path
                d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z"
                fill={colors.textPrimary}
              />
            </svg>
          </button>
        )}

        {/* Signal Strength */}
        <svg width="16" height="12" viewBox="0 0 16 12" fill="none">
          <rect x="0" y="8" width="2" height="4" fill={signalStrength >= 1 ? colors.textPrimary : colors.textTertiary} />
          <rect x="4" y="6" width="2" height="6" fill={signalStrength >= 2 ? colors.textPrimary : colors.textTertiary} />
          <rect x="8" y="4" width="2" height="8" fill={signalStrength >= 3 ? colors.textPrimary : colors.textTertiary} />
          <rect x="12" y="2" width="2" height="10" fill={signalStrength >= 4 ? colors.textPrimary : colors.textTertiary} />
        </svg>

        {/* WiFi Icon */}
        <svg width="18" height="12" viewBox="0 0 18 12" fill="none">
          <path
            d="M9 12C9.55 12 10 11.55 10 11C10 10.45 9.55 10 9 10C8.45 10 8 10.45 8 11C8 11.55 8.45 12 9 12ZM9 8C10.1 8 11.1 8.4 11.9 9.1L13.3 7.7C12.1 6.6 10.6 6 9 6C7.4 6 5.9 6.6 4.7 7.7L6.1 9.1C6.9 8.4 7.9 8 9 8ZM9 4C11.2 4 13.3 4.9 14.9 6.4L16.3 5C14.3 3.1 11.7 2 9 2C6.3 2 3.7 3.1 1.7 5L3.1 6.4C4.7 4.9 6.8 4 9 4Z"
            fill={colors.textPrimary}
          />
        </svg>

        {/* Battery Percentage */}
        <span className="text-xs">{batteryLevel}%</span>

        {/* Battery Icon */}
        <svg width="24" height="12" viewBox="0 0 24 12" fill="none">
          <rect
            x="1"
            y="2"
            width="20"
            height="8"
            rx="2"
            stroke={colors.textPrimary}
            strokeWidth="1"
            fill="none"
          />
          <rect
            x="3"
            y="4"
            width={`${(batteryLevel / 100) * 16}`}
            height="4"
            fill={batteryLevel > 20 ? colors.textPrimary : colors.error}
          />
          <rect
            x="21"
            y="5"
            width="2"
            height="2"
            fill={colors.textPrimary}
          />
        </svg>
      </div>
    </div>
  );
};

// Status Bar with Background Image (for specific pages)
export const StatusBarWithBackground: React.FC<StatusBarProps & { backgroundImage?: string }> = ({
  backgroundImage,
  ...props
}) => {
  return (
    <div 
      className="relative"
      style={{
        height: layout.headerHeight,
        backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      {/* Overlay for better text readability */}
      {backgroundImage && (
        <div 
          className="absolute inset-0"
          style={{
            background: 'rgba(0, 0, 0, 0.3)',
          }}
        />
      )}
      
      <div className="relative z-10">
        <StatusBar {...props} />
      </div>
    </div>
  );
};
