const express = require('express');
const axios = require('axios');

const app = express();
app.use(express.json());

// Bot configuration
const BOT_TOKEN = '8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk';
const WEBAPP_URL = 'https://tg.tlock.org/';

// Send message function
async function sendMessage(chatId, text, options = {}) {
  try {
    const response = await axios.post(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {
      chat_id: chatId,
      text: text,
      parse_mode: 'HTML',
      ...options
    });
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error.response?.data || error.message);
  }
}

// Send photo function
async function sendPhoto(chatId, photo, caption, options = {}) {
  try {
    const response = await axios.post(`https://api.telegram.org/bot${BOT_TOKEN}/sendPhoto`, {
      chat_id: chatId,
      photo: photo,
      caption: caption,
      parse_mode: 'HTML',
      ...options
    });
    return response.data;
  } catch (error) {
    console.error('Error sending photo:', error.response?.data || error.message);
  }
}

// Webhook endpoint
app.post('/webhook', async (req, res) => {
  const { message } = req.body;
  
  if (!message) {
    return res.status(200).send('OK');
  }

  const chatId = message.chat.id;
  const text = message.text;
  const firstName = message.from.first_name || 'User';

  console.log(`Received message: ${text} from ${firstName} (${chatId})`);

  try {
    if (text === '/start') {
      // Welcome message with inline keyboard
      const welcomeText = `🚀 <b>TLock is back!</b>

But we're back bigger and better than before.

Welcome to TLock: a new world with its own blockchain, games, apps, and rewards 🚀

Ready to start your adventure, ${firstName}?`;

      const keyboard = {
        inline_keyboard: [
          [
            {
              text: '🎮 Launch Tlock',
              web_app: { url: WEBAPP_URL }
            }
          ],
          [
            {
              text: '📖 Help',
              callback_data: 'help'
            },
            {
              text: '🎯 Game Info',
              callback_data: 'game_info'
            }
          ]
        ]
      };

      await sendMessage(chatId, welcomeText, {
        reply_markup: keyboard
      });

    } else if (text === '/help') {
      const helpText = `❓ <b>Help & Support</b>

<b>Available Commands:</b>
/start - 🚀 Launch Tlock Mini App
/help - ❓ Get help and support  
/game - 🎮 Game information

<b>How to Play:</b>
1. Tap "Launch Tlock" button
2. Start mining tokens
3. Complete daily tasks
4. Invite friends for bonuses
5. Level up and earn rewards!

<b>Need Support?</b>
Contact our support team for assistance.`;

      await sendMessage(chatId, helpText);

    } else if (text === '/game') {
      const gameText = `🎮 <b>About Tlock Game</b>

<b>Features:</b>
• 🪙 Token Mining
• 📈 Level System  
• 🎯 Daily Tasks
• 👥 Friend Referrals
• 🏆 Leaderboards
• 💎 Special Rewards

<b>How to Earn:</b>
• Mine tokens every few hours
• Complete daily missions
• Invite friends to join
• Participate in special events

Ready to play? Tap the button below!`;

      const keyboard = {
        inline_keyboard: [
          [
            {
              text: '🚀 Play Now',
              web_app: { url: WEBAPP_URL }
            }
          ]
        ]
      };

      await sendMessage(chatId, gameText, {
        reply_markup: keyboard
      });

    } else {
      // Default response for unknown commands
      const defaultText = `Hi ${firstName}! 👋

I didn't understand that command. Try:
/start - Launch Tlock
/help - Get help
/game - Game info

Or use the menu button below to play directly!`;

      await sendMessage(chatId, defaultText);
    }

  } catch (error) {
    console.error('Error processing message:', error);
    await sendMessage(chatId, 'Sorry, something went wrong. Please try again later.');
  }

  res.status(200).send('OK');
});

// Handle callback queries (inline button presses)
app.post('/webhook', async (req, res) => {
  const { callback_query } = req.body;
  
  if (callback_query) {
    const chatId = callback_query.message.chat.id;
    const data = callback_query.data;

    if (data === 'help') {
      // Same as /help command
      const helpText = `❓ <b>Help & Support</b>

<b>Available Commands:</b>
/start - 🚀 Launch Tlock Mini App
/help - ❓ Get help and support  
/game - 🎮 Game information

<b>How to Play:</b>
1. Tap "Launch Tlock" button
2. Start mining tokens
3. Complete daily tasks
4. Invite friends for bonuses
5. Level up and earn rewards!`;

      await sendMessage(chatId, helpText);
    } else if (data === 'game_info') {
      // Same as /game command
      const gameText = `🎮 <b>About Tlock Game</b>

<b>Features:</b>
• 🪙 Token Mining
• 📈 Level System  
• 🎯 Daily Tasks
• 👥 Friend Referrals
• 🏆 Leaderboards
• 💎 Special Rewards`;

      await sendMessage(chatId, gameText);
    }

    // Answer callback query
    await axios.post(`https://api.telegram.org/bot${BOT_TOKEN}/answerCallbackQuery`, {
      callback_query_id: callback_query.id
    });
  }

  res.status(200).send('OK');
});

// Health check endpoint
app.get('/', (req, res) => {
  res.send('Tlock Bot is running! 🚀');
});

// Start server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Bot server running on port ${PORT}`);
});

// Set webhook (run this once)
async function setWebhook() {
  try {
    const webhookUrl = 'https://your-domain.com/webhook'; // 替换为你的域名
    const response = await axios.post(`https://api.telegram.org/bot${BOT_TOKEN}/setWebhook`, {
      url: webhookUrl
    });
    console.log('Webhook set:', response.data);
  } catch (error) {
    console.error('Error setting webhook:', error.response?.data || error.message);
  }
}

// Uncomment to set webhook (only run once)
// setWebhook();
