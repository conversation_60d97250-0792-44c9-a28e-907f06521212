# Telegram Bot 配置指南

## 机器人信息
- **机器人名称**: Tlock
- **用户名**: @HabbyBabyBot
- **Token**: 8054527269:AAEjKEJ8xQjBHvSfg8dwzJ6wCaGP-DGX7yk

## 开发环境设置

### 1. 启动开发服务器
```bash
npm run dev
```
开发服务器通常运行在 `http://localhost:3002` 或 `http://localhost:3003`

### 2. 设置 ngrok 隧道
```bash
# 安装 ngrok (如果还没安装)
npm install -g ngrok

# 配置 authtoken (只需要做一次)
npx ngrok config add-authtoken YOUR_AUTHTOKEN

# 启动隧道 (假设开发服务器在端口 3002)
npx ngrok http 3002 --host-header=rewrite
```

### 3. 更新机器人配置
```bash
# 使用 ngrok 提供的 HTTPS URL 更新机器人配置
node scripts/update-bot-config.cjs https://your-ngrok-url.ngrok-free.app
```

## 邀请链接格式

### ✅ 正确格式
```
https://t.me/HabbyBabyBot?startapp=ref_INVITECODE
```

### 示例
```
https://t.me/HabbyBabyBot?startapp=ref_LIODQH
```

**注意**:
- 使用 `?startapp=` 格式，不是 `/app?startapp=`
- 邀请码前必须加 `ref_` 前缀

**重要**: 邀请码前必须加 `ref_` 前缀才能正常工作。

## 测试方式

### 1. 菜单按钮测试
1. 在 Telegram 中找到 @HabbyBabyBot
2. 发送 `/start` 命令
3. 点击底部的 "🚀 启动 Tlock" 按钮

### 2. 邀请链接测试
直接点击邀请链接，格式为：
```
https://t.me/HabbyBabyBot?startapp=ref_YOUR_INVITE_CODE
```

## 生产环境部署

### 1. 部署应用到生产服务器
确保应用部署到一个稳定的 HTTPS 域名

### 2. 更新机器人配置
```bash
node scripts/update-bot-config.cjs https://your-production-domain.com
```

### 3. 验证配置
- 测试菜单按钮是否正常工作
- 测试邀请链接是否正常工作

## 故障排除

### 问题 1: 邀请链接显示 "bot application not found"
- 确认邀请码格式正确 (必须有 `ref_` 前缀)
- 确认机器人配置已更新
- 等待几分钟让配置生效

### 问题 2: ngrok URL 无法访问
- 确认 ngrok 进程还在运行
- 确认开发服务器还在运行
- 检查 ngrok 免费版本的限制

### 问题 3: 菜单按钮不显示
- 重新发送 `/start` 命令
- 等待几分钟让配置同步
- 检查机器人配置是否正确更新

## API 端点

### 登录 API
- **URL**: https://api.tlock.xyz/api/mining/login
- **方法**: POST
- **参数**: id, first_name, last_name, username, auth_date, hash, invitationCode

### 挖矿 API
- **URL**: https://api.tlock.xyz/api/mining
- **方法**: GET
- **需要**: Authorization token

### 聊天室消息 API
- **URL**: https://api.tlock.xyz/api/mining/getChatRoomMessages
- **方法**: GET
- **需要**: Authorization token
