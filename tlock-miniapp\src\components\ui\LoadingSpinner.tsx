import React from 'react';
import { BaseComponentProps } from '../../types';

interface LoadingSpinnerProps extends BaseComponentProps {
  size?: number;
  color?: string;
  thickness?: number;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 24,
  color = 'currentColor',
  thickness = 2,
  className = '',
}) => {
  return (
    <div
      className={`inline-block animate-spin ${className}`}
      style={{
        width: size,
        height: size,
      }}
    >
      <svg
        className="w-full h-full"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke={color}
          strokeWidth={thickness}
          strokeOpacity="0.2"
        />
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke={color}
          strokeWidth={thickness}
          strokeLinecap="round"
          strokeDasharray="31.416"
          strokeDashoffset="23.562"
          className="animate-spin"
          style={{
            transformOrigin: '12px 12px',
          }}
        />
      </svg>
    </div>
  );
};

// Loading overlay component
interface LoadingOverlayProps extends BaseComponentProps {
  visible: boolean;
  message?: string;
  size?: number;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  message = 'Loading...',
  size = 40,
  className = '',
}) => {
  if (!visible) return null;

  return (
    <div
      className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${className}`}
    >
      <div className="bg-dark-surface rounded-2xl p-6 flex flex-col items-center space-y-4 mx-4">
        <LoadingSpinner size={size} color="#259AEE" />
        {message && (
          <p className="text-dark-text-primary text-center">{message}</p>
        )}
      </div>
    </div>
  );
};

// Inline loading component
interface InlineLoadingProps extends BaseComponentProps {
  message?: string;
  size?: number;
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
  message = 'Loading...',
  size = 24,
  className = '',
}) => {
  return (
    <div className={`flex items-center justify-center space-x-3 py-8 ${className}`}>
      <LoadingSpinner size={size} color="#259AEE" />
      {message && (
        <span className="text-dark-text-secondary">{message}</span>
      )}
    </div>
  );
};

// Skeleton loading component
interface SkeletonProps extends BaseComponentProps {
  width?: string | number;
  height?: string | number;
  rounded?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  rounded = false,
  className = '',
}) => {
  return (
    <div
      className={`bg-dark-surface animate-pulse ${rounded ? 'rounded-full' : 'rounded'} ${className}`}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

// Skeleton text lines
interface SkeletonTextProps extends BaseComponentProps {
  lines?: number;
  spacing?: string;
}

export const SkeletonText: React.FC<SkeletonTextProps> = ({
  lines = 3,
  spacing = 'space-y-2',
  className = '',
}) => {
  return (
    <div className={`${spacing} ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          height="1rem"
          width={index === lines - 1 ? '75%' : '100%'}
        />
      ))}
    </div>
  );
};
