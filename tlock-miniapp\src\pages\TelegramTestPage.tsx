import React, { useState, useEffect } from 'react';
import { telegramWebApp, isTelegramWebApp, getTelegramUser, getTelegramInitData } from '../utils/telegram';

export const TelegramTestPage: React.FC = () => {
  const [telegramData, setTelegramData] = useState<any>(null);
  const [isInTelegram, setIsInTelegram] = useState(false);

  useEffect(() => {
    setIsInTelegram(isTelegramWebApp());
    
    if (isTelegramWebApp()) {
      const user = getTelegramUser();
      const initData = getTelegramInitData();
      
      setTelegramData({
        user,
        initData,
        platform: (window as any).Telegram?.WebApp?.platform,
        version: (window as any).Telegram?.WebApp?.version,
        colorScheme: (window as any).Telegram?.WebApp?.colorScheme,
        themeParams: (window as any).Telegram?.WebApp?.themeParams,
        viewportHeight: (window as any).Telegram?.WebApp?.viewportHeight,
        isExpanded: (window as any).Telegram?.WebApp?.isExpanded,
      });
    }
  }, []);

  const handleHapticTest = (type: string) => {
    if (isTelegramWebApp()) {
      switch (type) {
        case 'impact-light':
          telegramWebApp.hapticFeedback('impact', 'light');
          break;
        case 'impact-medium':
          telegramWebApp.hapticFeedback('impact', 'medium');
          break;
        case 'impact-heavy':
          telegramWebApp.hapticFeedback('impact', 'heavy');
          break;
        case 'notification-success':
          telegramWebApp.hapticFeedback('notification', 'success');
          break;
        case 'notification-error':
          telegramWebApp.hapticFeedback('notification', 'error');
          break;
        case 'selection':
          telegramWebApp.hapticFeedback('selection');
          break;
      }
    }
  };

  const handleShowAlert = () => {
    telegramWebApp.showAlert('这是一个测试警告！');
  };

  const handleShowConfirm = async () => {
    const result = await telegramWebApp.showConfirm('确认要执行此操作吗？');
    telegramWebApp.showAlert(`用户选择：${result ? '确认' : '取消'}`);
  };

  const handleOpenLink = () => {
    telegramWebApp.openLink('https://telegram.org');
  };

  const handleMainButton = () => {
    if (isTelegramWebApp()) {
      telegramWebApp.showMainButton('测试主按钮', () => {
        telegramWebApp.showAlert('主按钮被点击！');
        telegramWebApp.hideMainButton();
      });
    }
  };

  const handleBackButton = () => {
    if (isTelegramWebApp()) {
      telegramWebApp.showBackButton(() => {
        telegramWebApp.showAlert('返回按钮被点击！');
        telegramWebApp.hideBackButton();
      });
    }
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-center">
          Telegram Web App 测试
        </h1>

        {/* 环境检测 */}
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">环境检测</h2>
          <p className={`text-sm ${isInTelegram ? 'text-green-400' : 'text-red-400'}`}>
            {isInTelegram ? '✅ 运行在Telegram环境中' : '❌ 不在Telegram环境中'}
          </p>
        </div>

        {/* Telegram数据 */}
        {isInTelegram && telegramData && (
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-2">Telegram数据</h2>
            <div className="text-xs space-y-2">
              <div>
                <strong>用户信息:</strong>
                <pre className="mt-1 p-2 bg-gray-900 rounded text-xs overflow-x-auto">
                  {JSON.stringify(telegramData.user, null, 2)}
                </pre>
              </div>
              <div>
                <strong>平台:</strong> {telegramData.platform}
              </div>
              <div>
                <strong>版本:</strong> {telegramData.version}
              </div>
              <div>
                <strong>主题:</strong> {telegramData.colorScheme}
              </div>
              <div>
                <strong>视口高度:</strong> {telegramData.viewportHeight}px
              </div>
              <div>
                <strong>是否展开:</strong> {telegramData.isExpanded ? '是' : '否'}
              </div>
            </div>
          </div>
        )}

        {/* 触觉反馈测试 */}
        {isInTelegram && (
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-3">触觉反馈测试</h2>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <button
                onClick={() => handleHapticTest('impact-light')}
                className="p-2 bg-blue-600 rounded hover:bg-blue-700"
              >
                轻微震动
              </button>
              <button
                onClick={() => handleHapticTest('impact-medium')}
                className="p-2 bg-blue-600 rounded hover:bg-blue-700"
              >
                中等震动
              </button>
              <button
                onClick={() => handleHapticTest('impact-heavy')}
                className="p-2 bg-blue-600 rounded hover:bg-blue-700"
              >
                强烈震动
              </button>
              <button
                onClick={() => handleHapticTest('notification-success')}
                className="p-2 bg-green-600 rounded hover:bg-green-700"
              >
                成功通知
              </button>
              <button
                onClick={() => handleHapticTest('notification-error')}
                className="p-2 bg-red-600 rounded hover:bg-red-700"
              >
                错误通知
              </button>
              <button
                onClick={() => handleHapticTest('selection')}
                className="p-2 bg-purple-600 rounded hover:bg-purple-700"
              >
                选择反馈
              </button>
            </div>
          </div>
        )}

        {/* UI控件测试 */}
        {isInTelegram && (
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-3">UI控件测试</h2>
            <div className="space-y-2">
              <button
                onClick={handleShowAlert}
                className="w-full p-2 bg-yellow-600 rounded hover:bg-yellow-700"
              >
                显示警告
              </button>
              <button
                onClick={handleShowConfirm}
                className="w-full p-2 bg-orange-600 rounded hover:bg-orange-700"
              >
                显示确认对话框
              </button>
              <button
                onClick={handleOpenLink}
                className="w-full p-2 bg-cyan-600 rounded hover:bg-cyan-700"
              >
                打开外部链接
              </button>
              <button
                onClick={handleMainButton}
                className="w-full p-2 bg-green-600 rounded hover:bg-green-700"
              >
                显示主按钮
              </button>
              <button
                onClick={handleBackButton}
                className="w-full p-2 bg-gray-600 rounded hover:bg-gray-700"
              >
                显示返回按钮
              </button>
            </div>
          </div>
        )}

        {/* 非Telegram环境提示 */}
        {!isInTelegram && (
          <div className="p-4 bg-yellow-900 border border-yellow-600 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-yellow-400">
              ⚠️ 测试说明
            </h2>
            <p className="text-sm text-yellow-200">
              此页面需要在Telegram Mini App环境中运行才能测试所有功能。
              请通过Telegram Bot启动此应用。
            </p>
          </div>
        )}

        {/* 返回按钮 */}
        <div className="mt-6">
          <button
            onClick={() => window.location.href = '/mining'}
            className="w-full p-3 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回主页
          </button>
        </div>
      </div>
    </div>
  );
};
