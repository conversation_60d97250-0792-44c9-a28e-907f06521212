#!/bin/bash

# Tlock Bot 日志管理脚本

# 自动检测脚本所在目录的父目录作为 BOT_DIR
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$BOT_DIR/logs"
LOG_FILE="$LOG_DIR/bot.log"
ERROR_LOG="$LOG_DIR/bot_error.log"

# 显示帮助
show_help() {
    echo "📋 Tlock Bot 日志管理工具"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  tail [行数]     实时查看日志 (默认50行)"
    echo "  error [行数]    查看错误日志 (默认20行)"
    echo "  search <关键词> 搜索日志内容"
    echo "  clean          清理旧日志"
    echo "  rotate         轮转日志文件"
    echo "  size           显示日志文件大小"
    echo "  archive        归档日志文件"
    echo ""
    echo "示例:"
    echo "  $0 tail 100     # 查看最近100行日志"
    echo "  $0 error        # 查看错误日志"
    echo "  $0 search 邀请码 # 搜索包含'邀请码'的日志"
    echo "  $0 clean        # 清理7天前的日志"
}

# 实时查看日志
tail_logs() {
    local lines=${1:-50}
    echo "📄 实时查看日志 (最近 $lines 行)..."
    echo "按 Ctrl+C 退出"
    echo ""
    
    if [ -f "$LOG_FILE" ]; then
        tail -n $lines -f "$LOG_FILE"
    else
        echo "❌ 日志文件不存在: $LOG_FILE"
    fi
}

# 查看错误日志
view_errors() {
    local lines=${1:-20}
    echo "❌ 错误日志 (最近 $lines 行):"
    echo ""
    
    if [ -f "$ERROR_LOG" ]; then
        tail -n $lines "$ERROR_LOG"
    else
        echo "✅ 无错误日志文件"
    fi
}

# 搜索日志
search_logs() {
    local keyword="$1"
    if [ -z "$keyword" ]; then
        echo "❌ 请提供搜索关键词"
        return 1
    fi
    
    echo "🔍 搜索关键词: '$keyword'"
    echo ""
    
    if [ -f "$LOG_FILE" ]; then
        grep -n --color=always "$keyword" "$LOG_FILE" | tail -20
    else
        echo "❌ 日志文件不存在"
    fi
}

# 显示日志文件大小
show_size() {
    echo "📊 日志文件大小:"
    echo ""
    
    if [ -f "$LOG_FILE" ]; then
        echo "普通日志: $(du -h "$LOG_FILE" | cut -f1) ($(wc -l < "$LOG_FILE") 行)"
    else
        echo "普通日志: 不存在"
    fi
    
    if [ -f "$ERROR_LOG" ]; then
        echo "错误日志: $(du -h "$ERROR_LOG" | cut -f1) ($(wc -l < "$ERROR_LOG") 行)"
    else
        echo "错误日志: 不存在"
    fi
    
    # 显示数据文件大小
    if [ -d "$BOT_DIR/data" ]; then
        echo ""
        echo "📁 数据文件:"
        find "$BOT_DIR/data" -name "*.json" -exec du -h {} \; | while read size file; do
            lines=$(wc -l < "$file" 2>/dev/null || echo "0")
            echo "  $(basename "$file"): $size ($lines 行)"
        done
    fi
}

# 清理旧日志
clean_logs() {
    echo "🧹 清理7天前的日志..."
    
    # 备份当前日志
    if [ -f "$LOG_FILE" ]; then
        local backup_file="$LOG_DIR/bot_$(date +%Y%m%d_%H%M%S).log"
        cp "$LOG_FILE" "$backup_file"
        echo "✅ 当前日志已备份到: $backup_file"
        
        # 清空当前日志
        > "$LOG_FILE"
        echo "✅ 当前日志已清空"
    fi
    
    # 删除7天前的备份文件
    find "$LOG_DIR" -name "bot_*.log" -mtime +7 -delete
    echo "✅ 7天前的备份文件已删除"
}

# 轮转日志
rotate_logs() {
    echo "🔄 轮转日志文件..."
    
    if [ -f "$LOG_FILE" ]; then
        local timestamp=$(date +%Y%m%d_%H%M%S)
        mv "$LOG_FILE" "$LOG_DIR/bot_$timestamp.log"
        echo "✅ 日志已轮转到: bot_$timestamp.log"
    fi
    
    if [ -f "$ERROR_LOG" ]; then
        local timestamp=$(date +%Y%m%d_%H%M%S)
        mv "$ERROR_LOG" "$LOG_DIR/error_$timestamp.log"
        echo "✅ 错误日志已轮转到: error_$timestamp.log"
    fi
    
    echo "✅ 日志轮转完成，新日志将从空文件开始"
}

# 归档日志
archive_logs() {
    echo "📦 归档日志文件..."
    
    local archive_dir="$LOG_DIR/archive"
    local date_str=$(date +%Y%m)
    local archive_file="$archive_dir/logs_$date_str.tar.gz"
    
    mkdir -p "$archive_dir"
    
    # 归档所有日志文件
    tar -czf "$archive_file" -C "$LOG_DIR" --exclude="archive" *.log 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ 日志已归档到: $archive_file"
        
        # 删除已归档的文件
        find "$LOG_DIR" -name "*.log" -not -name "bot.log" -not -name "bot_error.log" -delete
        echo "✅ 已归档的日志文件已删除"
    else
        echo "❌ 归档失败"
    fi
}

# 主逻辑
case "$1" in
    "tail")
        tail_logs "$2"
        ;;
    "error")
        view_errors "$2"
        ;;
    "search")
        search_logs "$2"
        ;;
    "clean")
        clean_logs
        ;;
    "rotate")
        rotate_logs
        ;;
    "size")
        show_size
        ;;
    "archive")
        archive_logs
        ;;
    "help"|"--help"|"-h"|"")
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "使用 $0 help 查看帮助"
        exit 1
        ;;
esac
