import React from 'react';
import { CardProps } from '../../types';

export const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  padding = 'md',
  border = true,
  shadow = false,
  className = '',
}) => {
  const baseClasses = [
    'bg-dark-surface',
    'rounded-2xl',
    'transition-all',
    'duration-200',
  ];

  const paddingClasses = {
    none: [],
    sm: ['p-3'],
    md: ['p-4'],
    lg: ['p-6'],
  };

  const borderClasses = border ? ['border', 'border-dark-border'] : [];
  const shadowClasses = shadow ? ['shadow-lg', 'shadow-black/10'] : [];

  const classes = [
    ...baseClasses,
    ...paddingClasses[padding],
    ...borderClasses,
    ...shadowClasses,
    className,
  ].join(' ');

  return (
    <div className={classes}>
      {(title || subtitle) && (
        <div className="mb-4">
          {title && (
            <h3 className="text-lg font-semibold text-dark-text-primary mb-1">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-sm text-dark-text-secondary">
              {subtitle}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  );
};

// Specialized card variants
interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  className = '',
}) => {
  return (
    <Card className={`text-center ${className}`} padding="md">
      {icon && (
        <div className="flex justify-center mb-3">
          <div className="w-12 h-12 bg-primary-500/20 rounded-xl flex items-center justify-center">
            {icon}
          </div>
        </div>
      )}
      
      <div className="space-y-1">
        <div className="text-2xl font-bold text-primary-500">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        
        <div className="text-sm text-dark-text-muted">
          {title}
        </div>
        
        {subtitle && (
          <div className="text-xs text-dark-text-secondary">
            {subtitle}
          </div>
        )}
        
        {trend && (
          <div className={`text-xs font-medium ${
            trend.isPositive ? 'text-green-500' : 'text-red-500'
          }`}>
            {trend.isPositive ? '+' : ''}{trend.value}%
          </div>
        )}
      </div>
    </Card>
  );
};

// Feature card for grid layouts
interface FeatureCardProps {
  title: string;
  description?: string;
  icon: React.ReactNode;
  onClick?: () => void;
  badge?: string | number;
  disabled?: boolean;
  className?: string;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  onClick,
  badge,
  disabled = false,
  className = '',
}) => {
  const isClickable = !!onClick && !disabled;
  
  const cardClasses = [
    'relative',
    isClickable ? 'cursor-pointer hover:bg-dark-surface/80 active:scale-95' : '',
    disabled ? 'opacity-50' : '',
    className,
  ].join(' ');

  return (
    <Card 
      className={cardClasses}
      padding="md"
      onClick={isClickable ? onClick : undefined}
    >
      {badge && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
          {badge}
        </div>
      )}
      
      <div className="flex flex-col items-center text-center space-y-3">
        <div className="w-12 h-12 bg-primary-500/20 rounded-xl flex items-center justify-center text-primary-500">
          {icon}
        </div>
        
        <div>
          <h3 className="font-semibold text-dark-text-primary mb-1">
            {title}
          </h3>
          {description && (
            <p className="text-sm text-dark-text-secondary">
              {description}
            </p>
          )}
        </div>
      </div>
    </Card>
  );
};

// Welcome/Hero card
interface WelcomeCardProps {
  title: string;
  subtitle?: string;
  avatar?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
}

export const WelcomeCard: React.FC<WelcomeCardProps> = ({
  title,
  subtitle,
  avatar,
  children,
  className = '',
}) => {
  return (
    <Card className={`text-center ${className}`} padding="lg" shadow>
      {avatar && (
        <div className="flex justify-center mb-4">
          {avatar}
        </div>
      )}
      
      <h2 className="text-2xl font-bold text-dark-text-primary mb-2">
        {title}
      </h2>
      
      {subtitle && (
        <p className="text-dark-text-secondary mb-6">
          {subtitle}
        </p>
      )}
      
      {children}
    </Card>
  );
};
